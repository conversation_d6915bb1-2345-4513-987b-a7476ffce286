/**
 * Tool Call Display - 工具调用显示组件
 * 
 * 显示工具调用的执行状态、参数配置和结果
 */

import React, { useState, useEffect } from 'react';
import { ToolCall, ToolResult } from '../../types';
import { CodeBlock } from './CodeBlock';
import './ToolCallDisplay.css';

export interface ToolCallDisplayProps {
  toolCall: ToolCall;
  onExecute?: (toolCall: ToolCall) => Promise<ToolResult>;
  result?: ToolResult;
  isExecuting?: boolean;
}

export const ToolCallDisplay: React.FC<ToolCallDisplayProps> = ({
  toolCall,
  onExecute,
  result,
  isExecuting = false
}) => {
  const [localResult, setLocalResult] = useState<ToolResult | null>(result || null);
  const [executing, setExecuting] = useState(isExecuting);
  const [expanded, setExpanded] = useState(false);
  const [parameters, setParameters] = useState(toolCall.parameters || {});

  // 工具配置映射
  const toolConfigs = {
    'refactor': {
      name: '代码重构',
      icon: '🔧',
      description: '重构和优化代码结构',
      color: '#3b82f6'
    },
    'generateTests': {
      name: '生成测试',
      icon: '🧪',
      description: '为代码生成单元测试',
      color: '#10b981'
    },
    'explainCode': {
      name: '代码解释',
      icon: '📖',
      description: '解释代码功能和逻辑',
      color: '#8b5cf6'
    },
    'executeTerminal': {
      name: '终端执行',
      icon: '💻',
      description: '执行终端命令',
      color: '#f59e0b'
    },
    'ragQuery': {
      name: 'RAG搜索',
      icon: '🔍',
      description: '智能代码和知识库搜索',
      color: '#ef4444'
    },
    'optimize': {
      name: '性能优化',
      icon: '⚡',
      description: '提供性能优化建议',
      color: '#06b6d4'
    }
  };

  const config = toolConfigs[toolCall.name as keyof typeof toolConfigs] || {
    name: toolCall.name,
    icon: '🛠️',
    description: '工具调用',
    color: '#6b7280'
  };

  // 执行工具调用
  const handleExecute = async () => {
    if (!onExecute || executing) return;

    setExecuting(true);
    try {
      const updatedToolCall = { ...toolCall, parameters };
      const result = await onExecute(updatedToolCall);
      setLocalResult(result);
    } catch (error) {
      setLocalResult({
        success: false,
        error: (error as Error).message,
        content: '',
        metadata: {}
      });
    } finally {
      setExecuting(false);
    }
  };

  // 更新参数
  const updateParameter = (key: string, value: any) => {
    setParameters(prev => ({ ...prev, [key]: value }));
  };

  // 渲染参数配置
  const renderParameterConfig = () => {
    if (!toolCall.parameterSchema) return null;

    return (
      <div className="parameter-config">
        <h4>参数配置</h4>
        {Object.entries(toolCall.parameterSchema).map(([key, schema]) => (
          <div key={key} className="parameter-item">
            <label className="parameter-label">
              {schema.title || key}
              {schema.required && <span className="required">*</span>}
            </label>
            {renderParameterInput(key, schema, parameters[key])}
            {schema.description && (
              <div className="parameter-description">{schema.description}</div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // 渲染参数输入控件
  const renderParameterInput = (key: string, schema: any, value: any) => {
    switch (schema.type) {
      case 'string':
        if (schema.enum) {
          return (
            <select
              value={value || ''}
              onChange={(e) => updateParameter(key, e.target.value)}
              className="parameter-select"
            >
              <option value="">请选择...</option>
              {schema.enum.map((option: string) => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          );
        }
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => updateParameter(key, e.target.value)}
            placeholder={schema.placeholder}
            className="parameter-input"
          />
        );
      
      case 'number':
        return (
          <input
            type="number"
            value={value || ''}
            onChange={(e) => updateParameter(key, Number(e.target.value))}
            min={schema.minimum}
            max={schema.maximum}
            step={schema.step || 1}
            className="parameter-input"
          />
        );
      
      case 'boolean':
        return (
          <label className="parameter-checkbox">
            <input
              type="checkbox"
              checked={value || false}
              onChange={(e) => updateParameter(key, e.target.checked)}
            />
            <span className="checkmark"></span>
          </label>
        );
      
      case 'array':
        return (
          <div className="parameter-array">
            {(value || []).map((item: any, index: number) => (
              <div key={index} className="array-item">
                <input
                  type="text"
                  value={item}
                  onChange={(e) => {
                    const newArray = [...(value || [])];
                    newArray[index] = e.target.value;
                    updateParameter(key, newArray);
                  }}
                  className="parameter-input"
                />
                <button
                  onClick={() => {
                    const newArray = (value || []).filter((_: any, i: number) => i !== index);
                    updateParameter(key, newArray);
                  }}
                  className="remove-button"
                >
                  ×
                </button>
              </div>
            ))}
            <button
              onClick={() => updateParameter(key, [...(value || []), ''])}
              className="add-button"
            >
              + 添加
            </button>
          </div>
        );
      
      default:
        return (
          <textarea
            value={value || ''}
            onChange={(e) => updateParameter(key, e.target.value)}
            placeholder={schema.placeholder}
            className="parameter-textarea"
            rows={3}
          />
        );
    }
  };

  // 渲染执行结果
  const renderResult = () => {
    if (!localResult) return null;

    return (
      <div className={`tool-result ${localResult.success ? 'success' : 'error'}`}>
        <div className="result-header">
          <span className="result-status">
            {localResult.success ? '✅ 执行成功' : '❌ 执行失败'}
          </span>
          {localResult.metadata?.duration && (
            <span className="result-duration">
              耗时: {localResult.metadata.duration}ms
            </span>
          )}
        </div>
        
        {localResult.error && (
          <div className="result-error">
            <strong>错误信息:</strong> {localResult.error}
          </div>
        )}
        
        {localResult.content && (
          <div className="result-content">
            {localResult.metadata?.contentType === 'code' ? (
              <CodeBlock
                code={localResult.content}
                language={localResult.metadata?.language || 'text'}
                fileName={localResult.metadata?.fileName}
              />
            ) : (
              <div className="text-content">
                {localResult.content}
              </div>
            )}
          </div>
        )}
        
        {localResult.metadata?.suggestions && (
          <div className="result-suggestions">
            <h5>相关建议:</h5>
            <ul>
              {localResult.metadata.suggestions.map((suggestion: string, index: number) => (
                <li key={index}>{suggestion}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="tool-call-display">
      {/* 工具头部 */}
      <div 
        className="tool-header"
        style={{ borderLeftColor: config.color }}
      >
        <div className="tool-info">
          <span className="tool-icon">{config.icon}</span>
          <div className="tool-details">
            <h3 className="tool-name">{config.name}</h3>
            <p className="tool-description">{config.description}</p>
          </div>
        </div>
        
        <div className="tool-actions">
          {!localResult && (
            <button
              className="expand-button"
              onClick={() => setExpanded(!expanded)}
              title={expanded ? '折叠配置' : '展开配置'}
            >
              <i className={`icon ${expanded ? 'icon-chevron-up' : 'icon-chevron-down'}`} />
            </button>
          )}
          
          {onExecute && !localResult && (
            <button
              className={`execute-button ${executing ? 'executing' : ''}`}
              onClick={handleExecute}
              disabled={executing}
            >
              {executing ? (
                <>
                  <div className="spinner"></div>
                  执行中...
                </>
              ) : (
                <>
                  <i className="icon icon-play" />
                  执行
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* 参数配置 */}
      {expanded && !localResult && renderParameterConfig()}

      {/* 执行结果 */}
      {renderResult()}

      {/* 执行进度 */}
      {executing && (
        <div className="execution-progress">
          <div className="progress-bar">
            <div className="progress-fill"></div>
          </div>
          <div className="progress-text">正在执行 {config.name}...</div>
        </div>
      )}
    </div>
  );
};

// 导出类型
export type { ToolCallDisplayProps };
