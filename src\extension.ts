import * as vscode from 'vscode';
import * as path from 'path';

/**
 * AI Agent Extension Entry Point
 *
 * This is the main entry point for the AI Agent VS Code extension.
 * It follows the new modular architecture design.
 */

/**
 * Simple Webview Provider for AI Agent Chat
 */
class AiAgentChatViewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'aiAgentChatView';
  private _view?: vscode.WebviewView;

  constructor(private readonly _extensionContext: vscode.ExtensionContext) {}

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        vscode.Uri.file(path.join(this._extensionContext.extensionPath, 'dist')),
        vscode.Uri.file(path.join(this._extensionContext.extensionPath, 'media')),
      ]
    };

    webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

    // Handle messages from the webview
    webviewView.webview.onDidReceiveMessage(
      message => {
        switch (message.type) {
          case 'info':
            vscode.window.showInformationMessage(message.text);
            return;
          case 'error':
            vscode.window.showErrorMessage(message.text);
            return;
        }
      },
      undefined,
      this._extensionContext.subscriptions
    );
  }

  private _getHtmlForWebview(webview: vscode.Webview) {
    // Get the local path to main script run in the webview, then convert it to a uri we can use in the webview.
    const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionContext.extensionUri, 'dist', 'webview.js'));

    // Do the same for the stylesheet.
    const styleResetUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionContext.extensionUri, 'media', 'reset.css'));
    const styleVSCodeUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionContext.extensionUri, 'media', 'vscode.css'));
    const styleMainUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionContext.extensionUri, 'media', 'main.css'));

    // Use a nonce to only allow a specific script to be run.
    const nonce = getNonce();

    return `<!DOCTYPE html>
			<html lang="en">
			<head>
				<meta charset="UTF-8">
				<!--
					Use a content security policy to only allow loading images from https or from our extension directory,
					and only allow scripts that have a specific nonce.
				-->
				<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<link href="${styleResetUri}" rel="stylesheet">
				<link href="${styleVSCodeUri}" rel="stylesheet">
				<link href="${styleMainUri}" rel="stylesheet">
				<title>AI Agent</title>
			</head>
			<body>
				<div class="container">
					<h1>AI Agent</h1>
					<p>欢迎使用 AI Agent！</p>
					<div class="chat-container">
						<div class="messages" id="messages">
							<div class="message system">
								<div class="message-content">
									AI Agent 已启动，准备为您提供编程助手服务。
								</div>
							</div>
						</div>
						<div class="input-container">
							<input type="text" id="messageInput" placeholder="输入您的问题..." />
							<button id="sendButton">发送</button>
						</div>
					</div>
				</div>
				<script nonce="${nonce}" src="${scriptUri}"></script>
				<script nonce="${nonce}">
					const vscode = acquireVsCodeApi();

					// Handle send button click
					document.getElementById('sendButton').addEventListener('click', () => {
						const input = document.getElementById('messageInput');
						const message = input.value.trim();
						if (message) {
							addMessage('user', message);
							input.value = '';
							// TODO: Send to AI service
							setTimeout(() => {
								addMessage('assistant', '这是一个临时回复。完整的AI功能正在开发中...');
							}, 1000);
						}
					});

					// Handle enter key
					document.getElementById('messageInput').addEventListener('keypress', (e) => {
						if (e.key === 'Enter') {
							document.getElementById('sendButton').click();
						}
					});

					function addMessage(role, content) {
						const messagesContainer = document.getElementById('messages');
						const messageDiv = document.createElement('div');
						messageDiv.className = 'message ' + role;
						messageDiv.innerHTML = '<div class="message-content">' + content + '</div>';
						messagesContainer.appendChild(messageDiv);
						messagesContainer.scrollTop = messagesContainer.scrollHeight;
					}
				</script>
			</body>
			</html>`;
  }
}

function getNonce() {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}

export function activate(context: vscode.ExtensionContext) {
  console.log('AI Agent extension is being activated...');

  // Register the webview provider
  const provider = new AiAgentChatViewProvider(context);
  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(AiAgentChatViewProvider.viewType, provider)
  );

  // Register commands
  context.subscriptions.push(
    vscode.commands.registerCommand('ai-agent.getContext.selection', () => {
      vscode.window.showInformationMessage('获取选中文本功能正在开发中...');
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('ai-agent.getContext.file', () => {
      vscode.window.showInformationMessage('获取活动文件功能正在开发中...');
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('ai-agent.runTerminal', () => {
      vscode.window.showInformationMessage('终端命令执行功能正在开发中...');
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('ai-agent.refactor', () => {
      vscode.window.showInformationMessage('代码重构功能正在开发中...');
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('ai-agent.explain', () => {
      vscode.window.showInformationMessage('代码解释功能正在开发中...');
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('ai-agent.addTests', () => {
      vscode.window.showInformationMessage('生成测试功能正在开发中...');
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('ai-agent.indexWorkspace', () => {
      vscode.window.showInformationMessage('工作区索引功能正在开发中...');
    })
  );

  // Temporary welcome message
  vscode.window.showInformationMessage('AI Agent extension activated! Ready for development.');

  console.log('AI Agent extension activated successfully!');
}

export function deactivate() {
  console.log('AI Agent extension is being deactivated...');

  // TODO: Cleanup resources
  // TODO: Save state
  // TODO: Dispose subscriptions

  console.log('AI Agent extension deactivated successfully!');
}
