import * as vscode from 'vscode';
import * as path from 'path';
import { EventBus } from './core/EventBus';
import { ConfigManager } from './core/ConfigManager';
import { ChatService } from './core/ChatService';

/**
 * AI Agent Extension Entry Point
 *
 * This is the main entry point for the AI Agent VS Code extension.
 * It follows the new modular architecture design.
 */

// 全局服务实例
let eventBus: EventBus;
let configManager: ConfigManager;
let chatService: ChatService;

/**
 * AI Agent Chat Webview Provider
 */
class AiAgentChatViewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'aiAgentChatView';
  private _view?: vscode.WebviewView;

  constructor(private readonly _extensionContext: vscode.ExtensionContext) {}

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        vscode.Uri.file(path.join(this._extensionContext.extensionPath, 'dist')),
        vscode.Uri.file(path.join(this._extensionContext.extensionPath, 'media')),
      ]
    };

    webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

    // Handle messages from the webview
    webviewView.webview.onDidReceiveMessage(
      async message => {
        switch (message.type) {
          case 'sendMessage':
            await this.handleSendMessage(message.text);
            return;
          case 'clearHistory':
            await this.handleClearHistory();
            return;
          case 'openConfig':
            await vscode.commands.executeCommand('ai-agent.configure');
            return;
          case 'checkConfig':
            await this.handleCheckConfig();
            return;
          case 'info':
            vscode.window.showInformationMessage(message.text);
            return;
          case 'error':
            vscode.window.showErrorMessage(message.text);
            return;
        }
      },
      undefined,
      this._extensionContext.subscriptions
    );
  }

  /**
   * 处理发送消息
   */
  private async handleSendMessage(text: string): Promise<void> {
    if (!chatService || !chatService.isReady()) {
      this.postMessage({
        type: 'configNeeded',
        message: 'AI Agent 需要配置才能使用，请点击下方按钮进行配置'
      });
      return;
    }

    try {
      // 显示用户消息
      this.postMessage({
        type: 'userMessage',
        message: text
      });

      // 开始流式响应
      this.postMessage({
        type: 'streamStart'
      });

      // 发送到聊天服务（流式）
      console.log('Sending streaming message to chat service:', text);

      let fullContent = '';
      let messageId = '';

      for await (const chunk of chatService.sendMessageStream({
        message: text,
        stream: true
      })) {
        if (chunk.id) {
          messageId = chunk.id;
        }

        if (chunk.delta.content) {
          fullContent += chunk.delta.content;

          // 发送流式内容块
          this.postMessage({
            type: 'streamChunk',
            content: chunk.delta.content,
            fullContent: fullContent,
            messageId: messageId
          });
        }

        // 处理工具调用
        if (chunk.delta.toolCalls) {
          this.postMessage({
            type: 'toolCalls',
            toolCalls: chunk.delta.toolCalls
          });
        }

        // 检查是否完成
        if (chunk.finishReason) {
          console.log('Stream finished with reason:', chunk.finishReason);
          break;
        }
      }

      // 流式响应完成
      this.postMessage({
        type: 'streamEnd',
        messageId: messageId,
        fullContent: fullContent
      });

      console.log('Streaming response completed:', messageId);

    } catch (error) {
      console.error('Chat error:', error);
      this.postMessage({
        type: 'streamError',
        message: `发送消息失败: ${(error as Error).message}`
      });
    }
  }

  /**
   * 处理清除历史
   */
  private async handleClearHistory(): Promise<void> {
    if (chatService) {
      chatService.clearConversationHistory();
      this.postMessage({
        type: 'historyCleared'
      });
    }
  }

  /**
   * 检查配置状态
   */
  private async handleCheckConfig(): Promise<void> {
    if (!configManager || !configManager.hasApiKey()) {
      this.postMessage({
        type: 'configNeeded',
        message: '需要配置 API Key 才能使用 AI Agent'
      });
    }
  }

  /**
   * 向webview发送消息
   */
  private postMessage(message: any): void {
    if (this._view) {
      this._view.webview.postMessage(message);
    }
  }

  private _getHtmlForWebview(webview: vscode.Webview) {
    // Get the local path to main script run in the webview, then convert it to a uri we can use in the webview.
    const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionContext.extensionUri, 'dist', 'webview.js'));

    // Do the same for the stylesheet.
    const styleResetUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionContext.extensionUri, 'media', 'reset.css'));
    const styleVSCodeUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionContext.extensionUri, 'media', 'vscode.css'));
    const styleMainUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionContext.extensionUri, 'media', 'main.css'));

    // Use a nonce to only allow a specific script to be run.
    const nonce = getNonce();

    return `<!DOCTYPE html>
			<html lang="en">
			<head>
				<meta charset="UTF-8">
				<!--
					Use a content security policy to only allow loading images from https or from our extension directory,
					and only allow scripts that have a specific nonce.
				-->
				<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<link href="${styleResetUri}" rel="stylesheet">
				<link href="${styleVSCodeUri}" rel="stylesheet">
				<link href="${styleMainUri}" rel="stylesheet">
				<title>AI Agent</title>
			</head>
			<body>
				<div class="container">
					<h1>AI Agent</h1>
					<p>欢迎使用 AI Agent！</p>
					<div class="chat-container">
						<div class="header">
							<button id="clearButton" class="clear-btn">清除历史</button>
						</div>
						<div class="messages" id="messages">
							<div class="message system">
								<div class="message-content">
									AI Agent 已启动！
								</div>
							</div>
							<div id="configPrompt" class="config-prompt" style="display: none;">
								<div class="config-content">
									<h3>🚀 欢迎使用 AI Agent</h3>
									<p>请先配置 API 以开始使用强大的 AI 编程助手功能</p>
									<button id="configButton" class="config-btn">配置 API</button>
								</div>
							</div>
						</div>
						<div id="loading" class="loading-indicator" style="display: none;">
							<div class="loading-spinner"></div>
							<span>AI正在思考...</span>
						</div>
						<div id="usage" class="usage-info" style="display: none;"></div>
						<div class="input-container">
							<textarea id="messageInput" placeholder="输入您的问题... (Shift+Enter换行)" rows="1"></textarea>
							<button id="sendButton">发送</button>
						</div>
					</div>
				</div>
				<script nonce="${nonce}" src="${scriptUri}"></script>
				<script nonce="${nonce}">
					const vscode = acquireVsCodeApi();

					// Handle send button click
					document.getElementById('sendButton').addEventListener('click', () => {
						const input = document.getElementById('messageInput');
						const message = input.value.trim();
						if (message) {
							input.value = '';
							vscode.postMessage({
								type: 'sendMessage',
								text: message
							});
						}
					});

					// Handle enter key
					document.getElementById('messageInput').addEventListener('keypress', (e) => {
						if (e.key === 'Enter' && !e.shiftKey) {
							e.preventDefault();
							document.getElementById('sendButton').click();
						}
					});

					// Handle clear button
					document.getElementById('clearButton')?.addEventListener('click', () => {
						vscode.postMessage({
							type: 'clearHistory'
						});
					});

					// Handle config button
					document.getElementById('configButton')?.addEventListener('click', () => {
						vscode.postMessage({
							type: 'openConfig'
						});
					});

					// Handle messages from extension
					window.addEventListener('message', event => {
						const message = event.data;
						switch (message.type) {
							case 'userMessage':
								addMessage('user', message.message);
								break;
							case 'assistantMessage':
								addMessage('assistant', message.message);
								if (message.usage) {
									showUsageInfo(message.usage);
								}
								break;
							case 'streamStart':
								startStreamingResponse();
								break;
							case 'streamChunk':
								updateStreamingResponse(message.content, message.fullContent, message.messageId);
								break;
							case 'streamEnd':
								endStreamingResponse(message.messageId, message.fullContent);
								break;
							case 'streamError':
								handleStreamError(message.message);
								break;
							case 'error':
								addMessage('system', 'Error: ' + message.message);
								break;
							case 'configNeeded':
								showConfigPrompt();
								break;
							case 'loading':
								showLoading(message.show);
								break;
							case 'historyCleared':
								clearMessages();
								break;
						}
					});

					let currentStreamingMessage = null;
					let streamingMessageId = null;

					function addMessage(role, content) {
						const messagesContainer = document.getElementById('messages');
						const messageDiv = document.createElement('div');
						messageDiv.className = 'message ' + role;
						messageDiv.innerHTML = '<div class="message-content">' + escapeHtml(content) + '</div>';
						messagesContainer.appendChild(messageDiv);
						messagesContainer.scrollTop = messagesContainer.scrollHeight;
						return messageDiv;
					}

					function startStreamingResponse() {
						const messagesContainer = document.getElementById('messages');
						currentStreamingMessage = document.createElement('div');
						currentStreamingMessage.className = 'message assistant streaming';
						currentStreamingMessage.innerHTML = '<div class="message-content"><span class="cursor">▋</span></div>';
						messagesContainer.appendChild(currentStreamingMessage);
						messagesContainer.scrollTop = messagesContainer.scrollHeight;
					}

					function updateStreamingResponse(newContent, fullContent, messageId) {
						if (currentStreamingMessage) {
							streamingMessageId = messageId;
							const contentDiv = currentStreamingMessage.querySelector('.message-content');
							contentDiv.innerHTML = escapeHtml(fullContent) + '<span class="cursor">▋</span>';

							// 自动滚动到底部
							const messagesContainer = document.getElementById('messages');
							messagesContainer.scrollTop = messagesContainer.scrollHeight;
						}
					}

					function endStreamingResponse(messageId, fullContent) {
						if (currentStreamingMessage) {
							const contentDiv = currentStreamingMessage.querySelector('.message-content');
							contentDiv.innerHTML = escapeHtml(fullContent);
							currentStreamingMessage.classList.remove('streaming');
							currentStreamingMessage = null;
							streamingMessageId = null;

							// 最终滚动
							const messagesContainer = document.getElementById('messages');
							messagesContainer.scrollTop = messagesContainer.scrollHeight;
						}
					}

					function handleStreamError(errorMessage) {
						if (currentStreamingMessage) {
							currentStreamingMessage.remove();
							currentStreamingMessage = null;
							streamingMessageId = null;
						}
						addMessage('system', 'Error: ' + errorMessage);
					}

					function showLoading(show) {
						const loadingDiv = document.getElementById('loading');
						if (loadingDiv) {
							loadingDiv.style.display = show ? 'block' : 'none';
						}
					}

					function showUsageInfo(usage) {
						const usageDiv = document.getElementById('usage');
						if (usageDiv && usage) {
							usageDiv.textContent = 'Tokens: ' + usage.totalTokens;
							usageDiv.style.display = 'block';
						}
					}

					function showConfigPrompt() {
						const configPrompt = document.getElementById('configPrompt');
						if (configPrompt) {
							configPrompt.style.display = 'block';
						}
					}

					function hideConfigPrompt() {
						const configPrompt = document.getElementById('configPrompt');
						if (configPrompt) {
							configPrompt.style.display = 'none';
						}
					}

					function clearMessages() {
						const messagesContainer = document.getElementById('messages');
						messagesContainer.innerHTML = '<div class="message system"><div class="message-content">对话历史已清除</div></div>';
						hideConfigPrompt();
					}

					function escapeHtml(text) {
						const div = document.createElement('div');
						div.textContent = text;
						return div.innerHTML;
					}

					// 初始化时检查是否需要显示配置提示
					window.addEventListener('load', () => {
						// 发送消息检查配置状态
						vscode.postMessage({
							type: 'checkConfig'
						});
					});
				</script>
			</body>
			</html>`;
  }
}

function getNonce() {
  let text = '';
  const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < 32; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}

export async function activate(context: vscode.ExtensionContext) {
  console.log('AI Agent extension is being activated...');

  try {
    // Initialize core services
    eventBus = new EventBus();

    configManager = new ConfigManager(eventBus, {
      secretStorage: context.secrets,
      globalState: context.globalState,
      workspaceState: context.workspaceState,
    });

    chatService = new ChatService(eventBus, configManager);

    // Initialize services
    await configManager.initialize();

    // 尝试初始化聊天服务，但不因配置问题而失败
    try {
      await chatService.initialize();
    } catch (error) {
      console.log('ChatService initialization deferred due to missing configuration');
    }

    // Register the webview provider
    const provider = new AiAgentChatViewProvider(context);
    context.subscriptions.push(
      vscode.window.registerWebviewViewProvider(AiAgentChatViewProvider.viewType, provider)
    );

    // Register commands
    context.subscriptions.push(
      vscode.commands.registerCommand('ai-agent.getContext.selection', () => {
        vscode.window.showInformationMessage('获取选中文本功能正在开发中...');
      })
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('ai-agent.getContext.file', () => {
        vscode.window.showInformationMessage('获取活动文件功能正在开发中...');
      })
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('ai-agent.runTerminal', () => {
        vscode.window.showInformationMessage('终端命令执行功能正在开发中...');
      })
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('ai-agent.refactor', () => {
        vscode.window.showInformationMessage('代码重构功能正在开发中...');
      })
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('ai-agent.explain', () => {
        vscode.window.showInformationMessage('代码解释功能正在开发中...');
      })
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('ai-agent.addTests', () => {
        vscode.window.showInformationMessage('生成测试功能正在开发中...');
      })
    );

    context.subscriptions.push(
      vscode.commands.registerCommand('ai-agent.indexWorkspace', () => {
        vscode.window.showInformationMessage('工作区索引功能正在开发中...');
      })
    );

    // Add configuration command
    context.subscriptions.push(
      vscode.commands.registerCommand('ai-agent.configure', async () => {
        await showConfigurationDialog();
      })
    );

    // Add quick DeepSeek setup command
    context.subscriptions.push(
      vscode.commands.registerCommand('ai-agent.quickSetupDeepSeek', async () => {
        await quickSetupDeepSeek();
      })
    );

    // Welcome message
    if (!configManager.hasApiKey()) {
      const result = await vscode.window.showInformationMessage(
        'AI Agent 已激活！请选择配置方式：',
        '快速配置 DeepSeek R1',
        '自定义配置'
      );
      if (result === '快速配置 DeepSeek R1') {
        await vscode.commands.executeCommand('ai-agent.quickSetupDeepSeek');
      } else if (result === '自定义配置') {
        await vscode.commands.executeCommand('ai-agent.configure');
      }
    } else {
      vscode.window.showInformationMessage('AI Agent 已激活，准备就绪！');
    }

    console.log('AI Agent extension activated successfully!');
  } catch (error) {
    console.error('Failed to activate AI Agent extension:', error);
    vscode.window.showErrorMessage(`AI Agent 激活失败: ${(error as Error).message}`);
  }
}

/**
 * 显示配置对话框
 */
async function showConfigurationDialog(): Promise<void> {
  // 首先选择API提供者
  const provider = await vscode.window.showQuickPick([
    {
      label: 'DeepSeek',
      description: 'DeepSeek API (推荐使用R1推理模型)',
      detail: 'Base URL: https://api.deepseek.com',
      value: 'deepseek'
    },
    {
      label: 'OpenAI',
      description: 'OpenAI API',
      detail: 'Base URL: https://api.openai.com/v1',
      value: 'openai'
    }
  ], {
    placeHolder: '选择API提供者',
    title: 'AI Agent 配置'
  });

  if (!provider) return;

  // 根据选择的提供者设置配置
  let baseUrl: string;
  let defaultModel: string;
  let keyPrompt: string;
  let keyPlaceholder: string;

  if (provider.value === 'deepseek') {
    baseUrl = 'https://api.deepseek.com';
    defaultModel = 'deepseek-reasoner';
    keyPrompt = '请输入您的 DeepSeek API Key';
    keyPlaceholder = 'sk-...';
  } else {
    baseUrl = 'https://api.openai.com/v1';
    defaultModel = 'gpt-4o';
    keyPrompt = '请输入您的 OpenAI API Key';
    keyPlaceholder = 'sk-...';
  }

  const apiKey = await vscode.window.showInputBox({
    prompt: keyPrompt,
    password: true,
    placeHolder: keyPlaceholder,
    validateInput: (value) => {
      if (!value || value.length < 10) {
        return 'API Key 不能为空且长度至少为10个字符';
      }
      if (!value.startsWith('sk-')) {
        return 'API Key 应该以 "sk-" 开头';
      }
      return null;
    }
  });

  if (apiKey) {
    try {
      // 更新配置
      await configManager.updateConfig({
        apiKey,
        baseUrl,
        model: defaultModel,
        provider: provider.value as any
      });

      // 重新初始化聊天服务
      try {
        await chatService.initialize();
        vscode.window.showInformationMessage(`${provider.label} API 配置成功！当前模型: ${defaultModel}`);
      } catch (error) {
        console.error('Failed to reinitialize chat service:', error);
        vscode.window.showErrorMessage(`配置成功但服务初始化失败: ${(error as Error).message}`);
      }
    } catch (error) {
      vscode.window.showErrorMessage(`配置失败: ${(error as Error).message}`);
    }
  }
}

/**
 * 快速设置DeepSeek配置
 */
async function quickSetupDeepSeek(): Promise<void> {
  try {
    console.log('Starting quick DeepSeek setup...');

    // 直接设置DeepSeek配置
    await configManager.updateConfig({
      apiKey: 'sk-99432e204bd94ad3a18a314a81cb14b2',
      baseUrl: 'https://api.deepseek.com',
      model: 'deepseek-reasoner',
      provider: 'openai', // 使用OpenAI兼容接口
      temperature: 0.2,
      maxTokens: 4096
    });

    console.log('Configuration updated, reinitializing chat service...');

    // 重新初始化聊天服务
    try {
      await chatService.initialize();
      console.log('Chat service reinitialized successfully');
      vscode.window.showInformationMessage('DeepSeek R1 配置成功！现在可以开始使用 AI Agent 了。');
    } catch (initError) {
      console.error('Failed to initialize chat service:', initError);
      vscode.window.showErrorMessage(`配置成功但服务初始化失败: ${(initError as Error).message}`);
    }
  } catch (error) {
    console.error('Quick setup failed:', error);
    vscode.window.showErrorMessage(`快速配置失败: ${(error as Error).message}`);
  }
}

export function deactivate() {
  console.log('AI Agent extension is being deactivated...');

  // TODO: Cleanup resources
  // TODO: Save state
  // TODO: Dispose subscriptions

  console.log('AI Agent extension deactivated successfully!');
}
