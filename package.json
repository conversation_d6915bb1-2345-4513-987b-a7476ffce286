{"name": "ai-agent", "displayName": "AI Agent", "description": "A powerful AI programming assistant with agent-like capabilities.", "version": "0.0.1", "publisher": "developer", "repository": {"type": "git", "url": ""}, "engines": {"vscode": "^1.84.0"}, "categories": ["Other"], "keywords": ["ai", "assistant", "programming", "code", "chat", "llm", "rag"], "activationEvents": ["onView:aiAgentChatView", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "ai-agent-view-container", "title": "AI Agent", "icon": "media/icon.svg"}]}, "views": {"ai-agent-view-container": [{"id": "aiAgentChatView", "name": "Cha<PERSON>", "type": "webview"}]}, "commands": [{"command": "ai-agent.getContext.selection", "title": "AI Agent: Get Selected Text", "icon": "$(symbol-key)"}, {"command": "ai-agent.getContext.file", "title": "AI Agent: Get Active File", "icon": "$(file-code)"}, {"command": "ai-agent.runTerminal", "title": "AI Agent: Run Terminal Command"}, {"command": "ai-agent.refactor", "title": "AI Agent: Refactor Code"}, {"command": "ai-agent.explain", "title": "AI Agent: Explain Code"}, {"command": "ai-agent.addTests", "title": "AI Agent: Generate Tests"}, {"command": "ai-agent.indexWorkspace", "title": "AI Agent: Index Workspace"}], "menus": {"editor/context": [{"when": "editorHasSelection", "command": "ai-agent.refactor", "group": "9_ai_agent@1"}, {"when": "editorHasSelection", "command": "ai-agent.explain", "group": "9_ai_agent@2"}, {"when": "editorHasSelection", "command": "ai-agent.addTests", "group": "9_ai_agent@3"}]}, "configuration": {"title": "AI Agent", "properties": {"ai-agent.apiKey": {"type": "string", "description": "OpenAI 或兼容接口的 API Key（将安全地存储在 VS Code Secret Storage）", "default": ""}, "ai-agent.baseUrl": {"type": "string", "description": "API Base URL", "default": "https://api.deepseek.com", "enum": ["https://api.deepseek.com", "https://api.openai.com/v1"], "enumDescriptions": ["DeepSeek API", "OpenAI API"]}, "ai-agent.model": {"type": "string", "description": "聊天 / 补全时使用的模型 ID", "default": "deepseek-reasoner", "enum": ["deepseek-reasoner", "deepseek-chat", "gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo"], "enumDescriptions": ["DeepSeek R1 推理模型 (推荐)", "DeepSeek V3 聊天模型", "GPT-4o", "GPT-4o Mini", "GPT-4 Turbo", "GPT-3.5 Turbo"]}, "ai-agent.provider": {"type": "string", "description": "LLM 提供者", "default": "openai", "enum": ["openai"], "enumDescriptions": ["OpenAI 兼容 API (支持 DeepSeek)"]}, "ai-agent.temperature": {"type": "number", "description": "completion 请求的 temperature 参数", "default": 0.2}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack --mode development", "watch": "webpack --mode development --watch", "package": "webpack --mode production --devtool hidden-source-map", "lint": "eslint src --ext ts,tsx --fix", "lint:check": "eslint src --ext ts,tsx", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "build": "npm run compile", "clean": "<PERSON><PERSON><PERSON> dist", "pretest": "npm run compile"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.12", "@types/markdown-it": "^13.0.7", "@types/node": "18.x", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/vscode": "^1.84.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "css-loader": "^6.8.1", "eslint": "^8.54.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "style-loader": "^3.3.3", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "typescript": "^5.3.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@lancedb/lancedb": "^0.20.0", "@types/uuid": "^9.0.7", "@xenova/transformers": "^2.17.1", "highlight.js": "^11.9.0", "markdown-it": "^14.0.0", "openai": "^4.20.1", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.1", "zustand": "^4.4.7"}}