/**
 * Knowledge Base Manager - 知识库管理器
 * 
 * 管理外部知识源的集成，包括Stack Overflow、官方文档、最佳实践等
 */

import * as vscode from 'vscode';
import * as path from 'path';
import { EventBus } from './EventBus';

export interface KnowledgeItem {
  id: string;
  type: 'stackoverflow' | 'documentation' | 'tutorial' | 'best-practice' | 'example';
  title: string;
  content: string;
  url?: string;
  tags: string[];
  language?: string;
  framework?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  rating: number;
  lastUpdated: Date;
  metadata: KnowledgeMetadata;
}

export interface KnowledgeMetadata {
  author?: string;
  votes?: number;
  views?: number;
  answers?: number;
  accepted?: boolean;
  source: string;
  relevanceScore?: number;
  cacheExpiry?: Date;
}

export interface KnowledgeQuery {
  query: string;
  language?: string;
  framework?: string;
  type?: KnowledgeItem['type'][];
  difficulty?: KnowledgeItem['difficulty'][];
  limit?: number;
}

export interface KnowledgeSource {
  name: string;
  type: 'api' | 'scraper' | 'local';
  enabled: boolean;
  config: any;
  lastSync?: Date;
  itemCount: number;
}

export class KnowledgeBaseManager {
  private eventBus: EventBus;
  private knowledgeItems: Map<string, KnowledgeItem> = new Map();
  private sources: Map<string, KnowledgeSource> = new Map();
  private queryCache: Map<string, KnowledgeItem[]> = new Map();
  
  // 配置参数
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24小时
  private readonly MAX_CACHE_SIZE = 10000;
  private readonly SYNC_INTERVAL = 60 * 60 * 1000; // 1小时

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.initializeDefaultSources();
  }

  /**
   * 初始化知识库
   */
  async initialize(): Promise<void> {
    await this.eventBus.emit({
      type: 'knowledge.manager_initializing',
      source: 'KnowledgeBaseManager'
    });

    try {
      // 加载本地知识库
      await this.loadLocalKnowledgeBase();
      
      // 启动同步任务
      this.startSyncTasks();

      await this.eventBus.emit({
        type: 'knowledge.manager_initialized',
        source: 'KnowledgeBaseManager',
        itemCount: this.knowledgeItems.size,
        sourceCount: this.sources.size
      });

      console.log('Knowledge Base Manager initialized successfully');
    } catch (error) {
      await this.eventBus.emit({
        type: 'knowledge.manager_init_failed',
        source: 'KnowledgeBaseManager',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 搜索知识库
   */
  async searchKnowledge(query: KnowledgeQuery): Promise<KnowledgeItem[]> {
    const startTime = Date.now();
    
    try {
      await this.eventBus.emit({
        type: 'knowledge.search_started',
        source: 'KnowledgeBaseManager',
        query: query.query
      });

      // 检查缓存
      const cacheKey = this.generateCacheKey(query);
      const cachedResults = this.queryCache.get(cacheKey);
      
      if (cachedResults && this.isCacheValid(cacheKey)) {
        return cachedResults;
      }

      // 执行搜索
      const results = await this.performSearch(query);
      
      // 缓存结果
      this.cacheResults(cacheKey, results);

      await this.eventBus.emit({
        type: 'knowledge.search_completed',
        source: 'KnowledgeBaseManager',
        query: query.query,
        resultCount: results.length,
        duration: Date.now() - startTime
      });

      return results;

    } catch (error) {
      await this.eventBus.emit({
        type: 'knowledge.search_failed',
        source: 'KnowledgeBaseManager',
        query: query.query,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 添加知识项
   */
  async addKnowledgeItem(item: Omit<KnowledgeItem, 'id' | 'lastUpdated'>): Promise<string> {
    const id = this.generateId();
    const knowledgeItem: KnowledgeItem = {
      ...item,
      id,
      lastUpdated: new Date()
    };

    this.knowledgeItems.set(id, knowledgeItem);
    
    await this.eventBus.emit({
      type: 'knowledge.item_added',
      source: 'KnowledgeBaseManager',
      itemId: id,
      type: item.type
    });

    return id;
  }

  /**
   * 更新知识项
   */
  async updateKnowledgeItem(id: string, updates: Partial<KnowledgeItem>): Promise<void> {
    const existing = this.knowledgeItems.get(id);
    if (!existing) {
      throw new Error(`Knowledge item not found: ${id}`);
    }

    const updated: KnowledgeItem = {
      ...existing,
      ...updates,
      id, // 确保ID不被覆盖
      lastUpdated: new Date()
    };

    this.knowledgeItems.set(id, updated);
    
    await this.eventBus.emit({
      type: 'knowledge.item_updated',
      source: 'KnowledgeBaseManager',
      itemId: id
    });
  }

  /**
   * 删除知识项
   */
  async removeKnowledgeItem(id: string): Promise<void> {
    const removed = this.knowledgeItems.delete(id);
    
    if (removed) {
      await this.eventBus.emit({
        type: 'knowledge.item_removed',
        source: 'KnowledgeBaseManager',
        itemId: id
      });
    }
  }

  /**
   * 同步外部知识源
   */
  async syncKnowledgeSources(): Promise<void> {
    await this.eventBus.emit({
      type: 'knowledge.sync_started',
      source: 'KnowledgeBaseManager'
    });

    try {
      const syncPromises: Promise<void>[] = [];
      
      for (const [name, source] of this.sources) {
        if (source.enabled) {
          syncPromises.push(this.syncSource(name, source));
        }
      }

      await Promise.allSettled(syncPromises);

      await this.eventBus.emit({
        type: 'knowledge.sync_completed',
        source: 'KnowledgeBaseManager',
        syncedSources: Array.from(this.sources.keys()).filter(name => this.sources.get(name)?.enabled)
      });

    } catch (error) {
      await this.eventBus.emit({
        type: 'knowledge.sync_failed',
        source: 'KnowledgeBaseManager',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 获取推荐内容
   */
  async getRecommendations(context: {
    language?: string;
    framework?: string;
    currentCode?: string;
    errorMessage?: string;
  }): Promise<KnowledgeItem[]> {
    const recommendations: KnowledgeItem[] = [];
    
    // 基于上下文生成推荐查询
    const queries = this.generateRecommendationQueries(context);
    
    for (const query of queries) {
      const results = await this.searchKnowledge({
        query,
        language: context.language,
        framework: context.framework,
        limit: 3
      });
      
      recommendations.push(...results);
    }

    // 去重并按相关性排序
    const uniqueRecommendations = this.deduplicateAndRank(recommendations, context);
    
    return uniqueRecommendations.slice(0, 10);
  }

  /**
   * 执行搜索
   */
  private async performSearch(query: KnowledgeQuery): Promise<KnowledgeItem[]> {
    const results: KnowledgeItem[] = [];
    const queryTerms = this.tokenizeQuery(query.query);
    
    for (const item of this.knowledgeItems.values()) {
      let score = 0;
      
      // 标题匹配
      score += this.calculateTextScore(queryTerms, item.title) * 3;
      
      // 内容匹配
      score += this.calculateTextScore(queryTerms, item.content);
      
      // 标签匹配
      for (const tag of item.tags) {
        score += this.calculateTextScore(queryTerms, tag) * 2;
      }
      
      // 语言匹配
      if (query.language && item.language === query.language) {
        score += 5;
      }
      
      // 框架匹配
      if (query.framework && item.framework === query.framework) {
        score += 5;
      }
      
      // 类型过滤
      if (query.type && query.type.length > 0 && !query.type.includes(item.type)) {
        continue;
      }
      
      // 难度过滤
      if (query.difficulty && query.difficulty.length > 0 && !query.difficulty.includes(item.difficulty)) {
        continue;
      }
      
      if (score > 0) {
        item.metadata.relevanceScore = score;
        results.push(item);
      }
    }
    
    // 按相关性排序
    results.sort((a, b) => (b.metadata.relevanceScore || 0) - (a.metadata.relevanceScore || 0));
    
    return results.slice(0, query.limit || 20);
  }

  /**
   * 计算文本评分
   */
  private calculateTextScore(queryTerms: string[], text: string): number {
    let score = 0;
    const textLower = text.toLowerCase();
    
    for (const term of queryTerms) {
      const matches = (textLower.match(new RegExp(term, 'g')) || []).length;
      score += matches;
    }
    
    return score;
  }

  /**
   * 分词查询
   */
  private tokenizeQuery(query: string): string[] {
    return query.toLowerCase()
      .split(/\s+/)
      .filter(term => term.length > 2)
      .map(term => term.replace(/[^\w]/g, ''));
  }

  /**
   * 生成推荐查询
   */
  private generateRecommendationQueries(context: {
    language?: string;
    framework?: string;
    currentCode?: string;
    errorMessage?: string;
  }): string[] {
    const queries: string[] = [];
    
    // 基于错误消息的查询
    if (context.errorMessage) {
      queries.push(context.errorMessage);
      queries.push(`${context.language} ${context.errorMessage}`);
    }
    
    // 基于代码内容的查询
    if (context.currentCode) {
      const codeKeywords = this.extractCodeKeywords(context.currentCode);
      for (const keyword of codeKeywords.slice(0, 3)) {
        queries.push(`${context.language} ${keyword} example`);
        queries.push(`${keyword} best practices`);
      }
    }
    
    // 基于语言和框架的通用查询
    if (context.language) {
      queries.push(`${context.language} best practices`);
      queries.push(`${context.language} common patterns`);
    }
    
    if (context.framework) {
      queries.push(`${context.framework} tutorial`);
      queries.push(`${context.framework} examples`);
    }
    
    return queries;
  }

  /**
   * 提取代码关键词
   */
  private extractCodeKeywords(code: string): string[] {
    const keywords: string[] = [];
    
    // 提取函数名
    const functionMatches = code.match(/function\s+(\w+)|(\w+)\s*\(/g);
    if (functionMatches) {
      for (const match of functionMatches) {
        const name = match.replace(/function\s+|[\s\(]/g, '');
        if (name && name.length > 2) {
          keywords.push(name);
        }
      }
    }
    
    // 提取类名
    const classMatches = code.match(/class\s+(\w+)/g);
    if (classMatches) {
      for (const match of classMatches) {
        const name = match.replace(/class\s+/g, '');
        keywords.push(name);
      }
    }
    
    // 提取导入的模块
    const importMatches = code.match(/import\s+.*from\s+['"]([^'"]+)['"]/g);
    if (importMatches) {
      for (const match of importMatches) {
        const module = match.match(/['"]([^'"]+)['"]$/)?.[1];
        if (module) {
          keywords.push(module);
        }
      }
    }
    
    return [...new Set(keywords)];
  }

  /**
   * 去重并排序
   */
  private deduplicateAndRank(items: KnowledgeItem[], context: any): KnowledgeItem[] {
    const seen = new Set<string>();
    const unique: KnowledgeItem[] = [];
    
    for (const item of items) {
      if (!seen.has(item.id)) {
        seen.add(item.id);
        unique.push(item);
      }
    }
    
    // 重新计算相关性评分
    for (const item of unique) {
      let contextScore = item.metadata.relevanceScore || 0;
      
      // 语言匹配加分
      if (context.language && item.language === context.language) {
        contextScore += 10;
      }
      
      // 框架匹配加分
      if (context.framework && item.framework === context.framework) {
        contextScore += 10;
      }
      
      // 评分和投票数加分
      contextScore += item.rating * 2;
      if (item.metadata.votes) {
        contextScore += Math.log(item.metadata.votes + 1);
      }
      
      item.metadata.relevanceScore = contextScore;
    }
    
    // 按相关性排序
    unique.sort((a, b) => (b.metadata.relevanceScore || 0) - (a.metadata.relevanceScore || 0));
    
    return unique;
  }

  /**
   * 同步单个知识源
   */
  private async syncSource(name: string, source: KnowledgeSource): Promise<void> {
    try {
      console.log(`Syncing knowledge source: ${name}`);
      
      switch (source.type) {
        case 'local':
          await this.syncLocalSource(name, source);
          break;
        case 'api':
          await this.syncApiSource(name, source);
          break;
        case 'scraper':
          await this.syncScraperSource(name, source);
          break;
      }
      
      source.lastSync = new Date();
      
    } catch (error) {
      console.error(`Failed to sync source ${name}:`, error);
    }
  }

  /**
   * 同步本地知识源
   */
  private async syncLocalSource(name: string, source: KnowledgeSource): Promise<void> {
    // 加载本地文档文件
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) return;

    for (const folder of workspaceFolders) {
      const docFiles = await vscode.workspace.findFiles(
        new vscode.RelativePattern(folder, '**/*.{md,txt,rst}'),
        '**/node_modules/**',
        100
      );

      for (const file of docFiles) {
        try {
          const content = await vscode.workspace.fs.readFile(file);
          const text = Buffer.from(content).toString('utf8');
          
          await this.addKnowledgeItem({
            type: 'documentation',
            title: path.basename(file.fsPath),
            content: text,
            url: file.toString(),
            tags: ['documentation', 'local'],
            difficulty: 'intermediate',
            rating: 3,
            metadata: {
              source: name,
              cacheExpiry: new Date(Date.now() + this.CACHE_TTL)
            }
          });
        } catch (error) {
          console.error(`Failed to process doc file ${file.fsPath}:`, error);
        }
      }
    }
  }

  /**
   * 同步API知识源
   */
  private async syncApiSource(name: string, source: KnowledgeSource): Promise<void> {
    // 这里可以集成Stack Overflow API、GitHub API等
    console.log(`API sync for ${name} - placeholder implementation`);
  }

  /**
   * 同步爬虫知识源
   */
  private async syncScraperSource(name: string, source: KnowledgeSource): Promise<void> {
    // 这里可以实现网页爬虫逻辑
    console.log(`Scraper sync for ${name} - placeholder implementation`);
  }

  /**
   * 初始化默认知识源
   */
  private initializeDefaultSources(): void {
    this.sources.set('local-docs', {
      name: 'Local Documentation',
      type: 'local',
      enabled: true,
      config: {},
      itemCount: 0
    });

    this.sources.set('stackoverflow', {
      name: 'Stack Overflow',
      type: 'api',
      enabled: false, // 默认禁用，需要API密钥
      config: {
        apiKey: '',
        baseUrl: 'https://api.stackexchange.com/2.3'
      },
      itemCount: 0
    });

    this.sources.set('mdn-docs', {
      name: 'MDN Web Docs',
      type: 'scraper',
      enabled: false,
      config: {
        baseUrl: 'https://developer.mozilla.org'
      },
      itemCount: 0
    });
  }

  /**
   * 加载本地知识库
   */
  private async loadLocalKnowledgeBase(): Promise<void> {
    // 加载预置的知识库内容
    await this.loadBuiltinKnowledge();
  }

  /**
   * 加载内置知识
   */
  private async loadBuiltinKnowledge(): Promise<void> {
    const builtinItems: Omit<KnowledgeItem, 'id' | 'lastUpdated'>[] = [
      {
        type: 'best-practice',
        title: 'TypeScript Best Practices',
        content: 'Use strict type checking, prefer interfaces over types for object shapes, use readonly for immutable data...',
        tags: ['typescript', 'best-practices', 'coding-standards'],
        language: 'typescript',
        difficulty: 'intermediate',
        rating: 5,
        metadata: {
          source: 'builtin',
          author: 'AI Agent'
        }
      },
      {
        type: 'example',
        title: 'React Hooks Example',
        content: 'const [count, setCount] = useState(0); useEffect(() => { document.title = `Count: ${count}`; }, [count]);',
        tags: ['react', 'hooks', 'javascript', 'example'],
        language: 'javascript',
        framework: 'react',
        difficulty: 'beginner',
        rating: 4,
        metadata: {
          source: 'builtin',
          author: 'AI Agent'
        }
      }
    ];

    for (const item of builtinItems) {
      await this.addKnowledgeItem(item);
    }
  }

  /**
   * 启动同步任务
   */
  private startSyncTasks(): void {
    // 定期同步知识源
    setInterval(() => {
      this.syncKnowledgeSources().catch(error => {
        console.error('Scheduled sync failed:', error);
      });
    }, this.SYNC_INTERVAL);
  }

  /**
   * 工具方法
   */
  private generateId(): string {
    return `kb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCacheKey(query: KnowledgeQuery): string {
    return JSON.stringify(query);
  }

  private isCacheValid(cacheKey: string): boolean {
    // 简化的缓存有效性检查
    return true; // 在实际实现中应该检查时间戳
  }

  private cacheResults(key: string, results: KnowledgeItem[]): void {
    if (this.queryCache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.queryCache.keys().next().value;
      this.queryCache.delete(firstKey);
    }
    
    this.queryCache.set(key, results);
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    totalItems: number;
    sourceCount: number;
    cacheSize: number;
    lastSyncTime?: Date;
  } {
    return {
      totalItems: this.knowledgeItems.size,
      sourceCount: this.sources.size,
      cacheSize: this.queryCache.size,
      lastSyncTime: Array.from(this.sources.values())
        .map(s => s.lastSync)
        .filter(Boolean)
        .sort((a, b) => b!.getTime() - a!.getTime())[0]
    };
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.knowledgeItems.clear();
    this.queryCache.clear();
  }
}
