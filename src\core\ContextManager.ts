/**
 * Context Manager - 上下文管理器
 * 
 * 负责管理对话历史的上下文窗口，实现智能截断和压缩
 */

import { Message } from '@/types';
import { EventBus } from './EventBus';

export interface ContextWindow {
  messages: Message[];
  totalTokens: number;
  maxTokens: number;
}

export interface ContextCompressionResult {
  compressedMessages: Message[];
  originalTokens: number;
  compressedTokens: number;
  compressionRatio: number;
}

export class ContextManager {
  private eventBus: EventBus;
  private maxContextTokens: number;
  private reservedTokens: number; // 为响应预留的token数量
  private compressionThreshold: number; // 触发压缩的阈值

  constructor(eventBus: EventBus, options: {
    maxContextTokens?: number;
    reservedTokens?: number;
    compressionThreshold?: number;
  } = {}) {
    this.eventBus = eventBus;
    this.maxContextTokens = options.maxContextTokens || 32000; // DeepSeek R1的上下文窗口
    this.reservedTokens = options.reservedTokens || 4096; // 为响应预留的token
    this.compressionThreshold = options.compressionThreshold || 0.8; // 80%时触发压缩
  }

  /**
   * 管理上下文窗口
   */
  async manageContext(messages: Message[]): Promise<Message[]> {
    // 估算当前token使用量
    const currentTokens = this.estimateTokens(messages);
    const availableTokens = this.maxContextTokens - this.reservedTokens;

    console.log(`Context management: ${currentTokens}/${availableTokens} tokens`);

    // 如果没有超出限制，直接返回
    if (currentTokens <= availableTokens) {
      return messages;
    }

    // 如果超出限制，需要进行上下文管理
    await this.eventBus.emit({
      type: 'context.management_triggered',
      source: 'ContextManager',
      currentTokens,
      availableTokens,
      messageCount: messages.length,
    });

    // 尝试智能截断
    let managedMessages = await this.intelligentTruncation(messages, availableTokens);

    // 如果截断后仍然超出，进行压缩
    const truncatedTokens = this.estimateTokens(managedMessages);
    if (truncatedTokens > availableTokens) {
      managedMessages = await this.compressContext(managedMessages, availableTokens);
    }

    const finalTokens = this.estimateTokens(managedMessages);
    console.log(`Context managed: ${finalTokens}/${availableTokens} tokens, ${managedMessages.length} messages`);

    await this.eventBus.emit({
      type: 'context.management_completed',
      source: 'ContextManager',
      originalTokens: currentTokens,
      finalTokens,
      originalMessageCount: messages.length,
      finalMessageCount: managedMessages.length,
    });

    return managedMessages;
  }

  /**
   * 智能截断策略
   */
  private async intelligentTruncation(messages: Message[], targetTokens: number): Promise<Message[]> {
    // 保留系统消息
    const systemMessages = messages.filter(msg => msg.type === 'system');
    let otherMessages = messages.filter(msg => msg.type !== 'system');

    // 从最新的消息开始保留
    const result: Message[] = [...systemMessages];
    let currentTokens = this.estimateTokens(systemMessages);

    // 从后往前添加消息，直到接近token限制
    for (let i = otherMessages.length - 1; i >= 0; i--) {
      const message = otherMessages[i];
      const messageTokens = this.estimateTokens([message]);
      
      if (currentTokens + messageTokens <= targetTokens * 0.9) { // 留10%缓冲
        result.splice(-systemMessages.length || result.length, 0, message);
        currentTokens += messageTokens;
      } else {
        break;
      }
    }

    // 确保对话的连贯性 - 如果截断了用户消息，也要截断对应的助手回复
    const finalMessages = this.ensureConversationCoherence(result);

    await this.eventBus.emit({
      type: 'context.truncation_applied',
      source: 'ContextManager',
      originalCount: messages.length,
      truncatedCount: finalMessages.length,
      tokensRemoved: this.estimateTokens(messages) - this.estimateTokens(finalMessages),
    });

    return finalMessages;
  }

  /**
   * 上下文压缩
   */
  private async compressContext(messages: Message[], targetTokens: number): Promise<Message[]> {
    // 简单的压缩策略：保留最重要的消息
    const systemMessages = messages.filter(msg => msg.type === 'system');
    const userMessages = messages.filter(msg => msg.type === 'user');
    const assistantMessages = messages.filter(msg => msg.type === 'assistant');

    // 保留最近的几轮对话
    const recentPairs = Math.min(3, Math.floor(userMessages.length / 2));
    const recentUserMessages = userMessages.slice(-recentPairs);
    const recentAssistantMessages = assistantMessages.slice(-recentPairs);

    // 创建压缩后的消息列表
    const compressedMessages: Message[] = [
      ...systemMessages,
      // 添加压缩摘要
      {
        id: `compressed_${Date.now()}`,
        type: 'system',
        content: this.createCompressionSummary(messages, recentPairs),
        timestamp: Date.now(),
        metadata: { compressed: true },
      },
      // 添加最近的对话
      ...this.interleaveMessages(recentUserMessages, recentAssistantMessages),
    ];

    await this.eventBus.emit({
      type: 'context.compression_applied',
      source: 'ContextManager',
      originalTokens: this.estimateTokens(messages),
      compressedTokens: this.estimateTokens(compressedMessages),
      compressionRatio: this.estimateTokens(compressedMessages) / this.estimateTokens(messages),
    });

    return compressedMessages;
  }

  /**
   * 确保对话连贯性
   */
  private ensureConversationCoherence(messages: Message[]): Message[] {
    const result: Message[] = [];
    const systemMessages = messages.filter(msg => msg.type === 'system');
    const conversationMessages = messages.filter(msg => msg.type !== 'system');

    // 添加系统消息
    result.push(...systemMessages);

    // 确保对话以用户消息开始，以助手消息结束（如果有的话）
    let expectingUser = true;
    for (const message of conversationMessages) {
      if (expectingUser && message.type === 'user') {
        result.push(message);
        expectingUser = false;
      } else if (!expectingUser && message.type === 'assistant') {
        result.push(message);
        expectingUser = true;
      }
    }

    return result;
  }

  /**
   * 交错排列用户和助手消息
   */
  private interleaveMessages(userMessages: Message[], assistantMessages: Message[]): Message[] {
    const result: Message[] = [];
    const maxLength = Math.max(userMessages.length, assistantMessages.length);

    for (let i = 0; i < maxLength; i++) {
      if (i < userMessages.length) {
        result.push(userMessages[i]);
      }
      if (i < assistantMessages.length) {
        result.push(assistantMessages[i]);
      }
    }

    return result.sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * 创建压缩摘要
   */
  private createCompressionSummary(originalMessages: Message[], recentPairsKept: number): string {
    const totalMessages = originalMessages.filter(msg => msg.type !== 'system').length;
    const compressedCount = totalMessages - (recentPairsKept * 2);

    return `[上下文摘要] 之前的对话包含${compressedCount}条消息，主要讨论了编程相关问题。为了保持对话流畅，已保留最近${recentPairsKept}轮对话的完整内容。`;
  }

  /**
   * 估算token数量（简单实现）
   */
  private estimateTokens(messages: Message[]): number {
    let totalTokens = 0;
    
    for (const message of messages) {
      const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
      // 简单估算：1个token约等于4个字符（对于中英文混合文本）
      totalTokens += Math.ceil(content.length / 3);
      
      // 为消息结构添加一些开销
      totalTokens += 10;
    }
    
    return totalTokens;
  }

  /**
   * 检查是否需要压缩
   */
  shouldCompress(messages: Message[]): boolean {
    const currentTokens = this.estimateTokens(messages);
    const threshold = (this.maxContextTokens - this.reservedTokens) * this.compressionThreshold;
    return currentTokens > threshold;
  }

  /**
   * 获取上下文统计信息
   */
  getContextStats(messages: Message[]): {
    totalMessages: number;
    totalTokens: number;
    availableTokens: number;
    utilizationRatio: number;
  } {
    const totalTokens = this.estimateTokens(messages);
    const availableTokens = this.maxContextTokens - this.reservedTokens;
    
    return {
      totalMessages: messages.length,
      totalTokens,
      availableTokens,
      utilizationRatio: totalTokens / availableTokens,
    };
  }

  /**
   * 更新配置
   */
  updateConfig(config: {
    maxContextTokens?: number;
    reservedTokens?: number;
    compressionThreshold?: number;
  }): void {
    if (config.maxContextTokens !== undefined) {
      this.maxContextTokens = config.maxContextTokens;
    }
    if (config.reservedTokens !== undefined) {
      this.reservedTokens = config.reservedTokens;
    }
    if (config.compressionThreshold !== undefined) {
      this.compressionThreshold = config.compressionThreshold;
    }
  }
}
