/**
 * Message Bubble - 消息气泡组件
 * 
 * 支持Markdown渲染、代码高亮、工具调用显示
 */

import React, { useState, useEffect, useRef } from 'react';
import { Message, ToolCall, ToolResult } from '../../types';
import { CodeBlock } from './CodeBlock';
import { ToolCallDisplay } from './ToolCallDisplay';
import './MessageBubble.css';

export interface MessageBubbleProps {
  message: Message;
  onToolCall?: (toolCall: ToolCall) => Promise<ToolResult>;
  isStreaming?: boolean;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  onToolCall,
  isStreaming = false
}) => {
  const [renderedContent, setRenderedContent] = useState<string>('');
  const [isExpanded, setIsExpanded] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // 解析Markdown内容
  useEffect(() => {
    const parseMarkdown = (content: string): string => {
      let html = content;

      // 代码块处理
      html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
        return `<div class="code-block-container" data-language="${language || 'text'}">${code.trim()}</div>`;
      });

      // 行内代码
      html = html.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

      // 标题
      html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
      html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
      html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

      // 粗体和斜体
      html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

      // 链接
      html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

      // 列表
      html = html.replace(/^- (.*$)/gm, '<li>$1</li>');
      html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

      // 换行
      html = html.replace(/\n/g, '<br>');

      return html;
    };

    setRenderedContent(parseMarkdown(message.content));
  }, [message.content]);

  // 渲染代码块
  const renderCodeBlocks = () => {
    if (!contentRef.current) return;

    const codeBlocks = contentRef.current.querySelectorAll('.code-block-container');
    codeBlocks.forEach((block, index) => {
      const language = block.getAttribute('data-language') || 'text';
      const code = block.textContent || '';
      
      // 创建React组件并渲染
      const codeBlockElement = document.createElement('div');
      block.parentNode?.replaceChild(codeBlockElement, block);
      
      // 这里应该使用ReactDOM.render，但为了简化，我们使用innerHTML
      codeBlockElement.innerHTML = `
        <div class="code-block">
          <div class="code-header">
            <span class="language-label">${language}</span>
            <button class="copy-button" onclick="navigator.clipboard.writeText('${code.replace(/'/g, "\\'")}')">
              <i class="icon icon-copy"></i>
              复制
            </button>
          </div>
          <pre><code class="language-${language}">${escapeHtml(code)}</code></pre>
        </div>
      `;
    });
  };

  useEffect(() => {
    renderCodeBlocks();
  }, [renderedContent]);

  // HTML转义
  const escapeHtml = (text: string): string => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  };

  // 处理消息展开/折叠
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // 判断是否需要折叠
  const shouldCollapse = message.content.length > 1000;

  return (
    <div className={`message-bubble ${message.role} ${isStreaming ? 'streaming' : ''}`}>
      {/* 消息头部 */}
      <div className="message-header">
        <div className="message-avatar">
          {message.role === 'user' ? (
            <i className="icon icon-user" />
          ) : (
            <i className="icon icon-robot" />
          )}
        </div>
        <div className="message-meta">
          <span className="message-role">
            {message.role === 'user' ? '您' : 'AI Agent'}
          </span>
          <span className="message-time">
            {message.timestamp?.toLocaleTimeString()}
          </span>
        </div>
        {shouldCollapse && (
          <button 
            className="expand-button"
            onClick={toggleExpanded}
            title={isExpanded ? '折叠消息' : '展开消息'}
          >
            <i className={`icon ${isExpanded ? 'icon-chevron-up' : 'icon-chevron-down'}`} />
          </button>
        )}
      </div>

      {/* 消息内容 */}
      <div 
        className={`message-content ${shouldCollapse && !isExpanded ? 'collapsed' : ''}`}
        ref={contentRef}
        dangerouslySetInnerHTML={{ __html: renderedContent }}
      />

      {/* 流式输入指示器 */}
      {isStreaming && (
        <div className="streaming-indicator">
          <div className="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      )}

      {/* 工具调用显示 */}
      {message.toolCalls && message.toolCalls.length > 0 && (
        <div className="tool-calls">
          {message.toolCalls.map((toolCall, index) => (
            <ToolCallDisplay
              key={index}
              toolCall={toolCall}
              onExecute={onToolCall}
            />
          ))}
        </div>
      )}

      {/* 消息操作 */}
      <div className="message-actions">
        <button 
          className="action-button"
          onClick={() => navigator.clipboard.writeText(message.content)}
          title="复制消息"
        >
          <i className="icon icon-copy" />
        </button>
        {message.role === 'assistant' && (
          <>
            <button 
              className="action-button"
              title="重新生成"
            >
              <i className="icon icon-refresh" />
            </button>
            <button 
              className="action-button"
              title="点赞"
            >
              <i className="icon icon-thumbs-up" />
            </button>
            <button 
              className="action-button"
              title="点踩"
            >
              <i className="icon icon-thumbs-down" />
            </button>
          </>
        )}
      </div>
    </div>
  );
};
