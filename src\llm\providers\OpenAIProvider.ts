/**
 * OpenAI Provider - OpenAI API集成
 * 
 * 实现OpenAI API的LLM提供者
 */

import OpenAI from 'openai';
import { 
  ILLMProvider, 
  LLMRequestConfig, 
  LLMResponse, 
  LLMStreamChunk, 
  ModelInfo, 
  HealthStatus,
  OpenAIConfig,
  LLMError,
  RateLimitError,
  QuotaExceededError,
  ModelUnavailableError,
  ToolCallError
} from '../interfaces';
import { Message, ToolCall, ToolResult, LLMProvider } from '@/types';

export class OpenAIProvider implements ILLMProvider {
  public readonly name: LLMProvider = 'openai';
  public readonly supportsStreaming = true;
  public readonly supportsTools = true;
  public readonly supportsVision = true;

  private client: OpenAI;
  private config: OpenAIConfig;

  public readonly models = [
    'gpt-4o',
    'gpt-4o-mini',
    'gpt-4-turbo',
    'gpt-4',
    'gpt-3.5-turbo',
    'gpt-3.5-turbo-16k',
    'deepseek-chat',
    'deepseek-reasoner',
  ];

  private modelInfoMap: Record<string, ModelInfo> = {
    'gpt-4o': {
      name: 'gpt-4o',
      displayName: 'GPT-4o',
      description: 'Most advanced multimodal model',
      maxTokens: 4096,
      inputCost: 0.005,
      outputCost: 0.015,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 128000,
    },
    'gpt-4o-mini': {
      name: 'gpt-4o-mini',
      displayName: 'GPT-4o Mini',
      description: 'Affordable and intelligent small model',
      maxTokens: 16384,
      inputCost: 0.00015,
      outputCost: 0.0006,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 128000,
    },
    'gpt-4-turbo': {
      name: 'gpt-4-turbo',
      displayName: 'GPT-4 Turbo',
      description: 'Latest GPT-4 model with improved performance',
      maxTokens: 4096,
      inputCost: 0.01,
      outputCost: 0.03,
      supportsTools: true,
      supportsVision: true,
      contextWindow: 128000,
    },
    'gpt-4': {
      name: 'gpt-4',
      displayName: 'GPT-4',
      description: 'Large multimodal model',
      maxTokens: 4096,
      inputCost: 0.03,
      outputCost: 0.06,
      supportsTools: true,
      supportsVision: false,
      contextWindow: 8192,
    },
    'gpt-3.5-turbo': {
      name: 'gpt-3.5-turbo',
      displayName: 'GPT-3.5 Turbo',
      description: 'Fast and efficient model',
      maxTokens: 4096,
      inputCost: 0.0015,
      outputCost: 0.002,
      supportsTools: true,
      supportsVision: false,
      contextWindow: 16385,
    },
    'deepseek-chat': {
      name: 'deepseek-chat',
      displayName: 'DeepSeek Chat',
      description: 'DeepSeek-V3 chat model',
      maxTokens: 4096,
      inputCost: 0.00014,
      outputCost: 0.00028,
      supportsTools: true,
      supportsVision: false,
      contextWindow: 64000,
    },
    'deepseek-reasoner': {
      name: 'deepseek-reasoner',
      displayName: 'DeepSeek Reasoner (R1)',
      description: 'DeepSeek-R1 reasoning model with advanced problem-solving capabilities',
      maxTokens: 8192,
      inputCost: 0.00055,
      outputCost: 0.0022,
      supportsTools: true,
      supportsVision: false,
      contextWindow: 64000,
    },
  };

  constructor(config: OpenAIConfig) {
    this.config = config;
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseUrl,
      organization: config.organization,
      timeout: config.timeout || 60000,
    });
  }

  configure(config: OpenAIConfig): void {
    this.config = { ...this.config, ...config };
    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      baseURL: this.config.baseUrl,
      organization: this.config.organization,
      timeout: this.config.timeout || 60000,
    });
  }

  async validateConfig(): Promise<boolean> {
    try {
      // 对于DeepSeek API，我们可以尝试一个简单的聊天请求来验证
      if (this.config.baseUrl?.includes('deepseek.com')) {
        const response = await this.client.chat.completions.create({
          model: 'deepseek-chat',
          messages: [{ role: 'user', content: 'Hello' }],
          max_tokens: 1,
        });
        return !!response;
      } else {
        // 对于OpenAI，使用models.list()
        await this.client.models.list();
        return true;
      }
    } catch (error) {
      console.error('Config validation failed:', error);
      return false;
    }
  }

  getModelInfo(model: string): ModelInfo | undefined {
    return this.modelInfoMap[model];
  }

  async healthCheck(): Promise<HealthStatus> {
    const startTime = Date.now();
    
    try {
      await this.client.models.list();
      const latency = Date.now() - startTime;
      
      return {
        status: 'healthy',
        latency,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: (error as Error).message,
        timestamp: Date.now(),
      };
    }
  }

  async chat(messages: Message[], config: LLMRequestConfig): Promise<LLMResponse> {
    try {
      const openaiMessages = this.convertMessages(messages);
      const tools = config.tools ? this.convertTools(config.tools) : undefined;

      const response = await this.client.chat.completions.create({
        model: config.model,
        messages: openaiMessages,
        temperature: config.temperature,
        max_tokens: config.maxTokens,
        top_p: config.topP,
        frequency_penalty: config.frequencyPenalty,
        presence_penalty: config.presencePenalty,
        stop: config.stop,
        tools,
        tool_choice: config.toolChoice,
      });

      const choice = response.choices[0];
      if (!choice) {
        throw new LLMError('No response from OpenAI', 'openai', 'NO_RESPONSE');
      }

      const toolCalls = choice.message.tool_calls?.map(tc => ({
        id: tc.id,
        name: tc.function.name,
        arguments: JSON.parse(tc.function.arguments),
      }));

      return {
        id: response.id,
        content: choice.message.content || '',
        role: 'assistant',
        toolCalls,
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens,
        } : undefined,
        model: response.model,
        finishReason: choice.finish_reason as any,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async* chatStream(messages: Message[], config: LLMRequestConfig): AsyncIterable<LLMStreamChunk> {
    try {
      const openaiMessages = this.convertMessages(messages);
      const tools = config.tools ? this.convertTools(config.tools) : undefined;

      const stream = await this.client.chat.completions.create({
        model: config.model,
        messages: openaiMessages,
        temperature: config.temperature,
        max_tokens: config.maxTokens,
        top_p: config.topP,
        frequency_penalty: config.frequencyPenalty,
        presence_penalty: config.presencePenalty,
        stop: config.stop,
        tools,
        tool_choice: config.toolChoice,
        stream: true,
      });

      for await (const chunk of stream) {
        const choice = chunk.choices[0];
        if (!choice) continue;

        const delta = choice.delta;
        const toolCalls = delta.tool_calls?.map(tc => ({
          id: tc.id,
          name: tc.function?.name,
          arguments: tc.function?.arguments ? JSON.parse(tc.function.arguments) : undefined,
        }));

        yield {
          id: chunk.id,
          delta: {
            content: delta.content || undefined,
            toolCalls,
          },
          finishReason: choice.finish_reason || undefined,
        };
      }
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async callTool(toolCall: ToolCall, context?: any): Promise<ToolResult> {
    // 工具调用的具体实现将在工具集成模块中处理
    // 这里只是返回一个占位符结果
    return {
      id: toolCall.id,
      result: `Tool ${toolCall.name} called with arguments: ${JSON.stringify(toolCall.arguments)}`,
    };
  }

  private convertMessages(messages: Message[]): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map(msg => {
      if (msg.type === 'user') {
        return {
          role: 'user',
          content: msg.content as string,
        };
      } else if (msg.type === 'assistant') {
        const toolCalls = msg.toolCalls?.map(tc => ({
          id: tc.id,
          type: 'function' as const,
          function: {
            name: tc.name,
            arguments: JSON.stringify(tc.arguments),
          },
        }));

        return {
          role: 'assistant',
          content: msg.content as string,
          tool_calls: toolCalls,
        };
      } else if (msg.type === 'tool') {
        return {
          role: 'tool',
          content: msg.content as string,
          tool_call_id: msg.metadata?.toolCallId || '',
        };
      } else {
        return {
          role: 'system',
          content: msg.content as string,
        };
      }
    });
  }

  private convertTools(tools: any[]): OpenAI.Chat.Completions.ChatCompletionTool[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      },
    }));
  }

  private handleError(error: any): Error {
    if (error.status === 429) {
      const retryAfter = error.headers?.['retry-after'];
      return new RateLimitError('openai', retryAfter ? parseInt(retryAfter) : undefined, error);
    }

    if (error.status === 402) {
      return new QuotaExceededError('openai', error);
    }

    if (error.status === 404) {
      return new ModelUnavailableError('openai', 'unknown', error);
    }

    return new LLMError(
      error.message || 'Unknown OpenAI error',
      'openai',
      error.code || 'UNKNOWN_ERROR',
      error.status,
      error
    );
  }
}
