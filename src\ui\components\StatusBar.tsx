/**
 * Status Bar - 状态栏组件
 * 
 * 显示AI处理状态、token使用情况、系统性能指标
 */

import React, { useState, useEffect } from 'react';
import './StatusBar.css';

export interface StatusBarProps {
  systemStats?: {
    tokenUsage: number;
    maxTokens: number;
    responseTime: number;
    ragStats: any;
  };
  isLoading?: boolean;
  messageCount?: number;
  connectionStatus?: 'connected' | 'connecting' | 'disconnected';
}

export const StatusBar: React.FC<StatusBarProps> = ({
  systemStats,
  isLoading = false,
  messageCount = 0,
  connectionStatus = 'connected'
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showDetails, setShowDetails] = useState(false);

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 获取连接状态信息
  const getConnectionInfo = () => {
    switch (connectionStatus) {
      case 'connected':
        return { icon: '🟢', text: '已连接', color: '#10b981' };
      case 'connecting':
        return { icon: '🟡', text: '连接中', color: '#f59e0b' };
      case 'disconnected':
        return { icon: '🔴', text: '已断开', color: '#ef4444' };
      default:
        return { icon: '⚪', text: '未知', color: '#6b7280' };
    }
  };

  const connectionInfo = getConnectionInfo();

  // 计算token使用百分比
  const tokenUsagePercent = systemStats ? 
    (systemStats.tokenUsage / systemStats.maxTokens) * 100 : 0;

  // 格式化数字
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // 格式化时间
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="status-bar">
      {/* 左侧状态信息 */}
      <div className="status-left">
        {/* 连接状态 */}
        <div 
          className="status-item connection-status"
          style={{ color: connectionInfo.color }}
        >
          <span className="status-icon">{connectionInfo.icon}</span>
          <span className="status-text">{connectionInfo.text}</span>
        </div>

        {/* 加载状态 */}
        {isLoading && (
          <div className="status-item loading-status">
            <div className="loading-spinner"></div>
            <span className="status-text">AI思考中...</span>
          </div>
        )}

        {/* 消息计数 */}
        <div className="status-item message-count">
          <i className="icon icon-message" />
          <span className="status-text">{messageCount} 条消息</span>
        </div>
      </div>

      {/* 中间性能指标 */}
      <div className="status-center">
        {systemStats && (
          <>
            {/* Token使用情况 */}
            <div 
              className="status-item token-usage"
              onClick={() => setShowDetails(!showDetails)}
              title="点击查看详细信息"
            >
              <div className="token-info">
                <span className="token-text">
                  Token: {formatNumber(systemStats.tokenUsage)}/{formatNumber(systemStats.maxTokens)}
                </span>
                <div className="token-bar">
                  <div 
                    className="token-fill"
                    style={{ 
                      width: `${tokenUsagePercent}%`,
                      backgroundColor: tokenUsagePercent > 90 ? '#ef4444' : 
                                     tokenUsagePercent > 70 ? '#f59e0b' : '#10b981'
                    }}
                  />
                </div>
              </div>
            </div>

            {/* 响应时间 */}
            <div className="status-item response-time">
              <i className="icon icon-clock" />
              <span className="status-text">{systemStats.responseTime}ms</span>
            </div>

            {/* RAG统计 */}
            {systemStats.ragStats && (
              <div className="status-item rag-stats">
                <i className="icon icon-database" />
                <span className="status-text">
                  索引: {formatNumber(systemStats.ragStats.indexStats?.totalItems || 0)}
                </span>
              </div>
            )}
          </>
        )}
      </div>

      {/* 右侧系统信息 */}
      <div className="status-right">
        {/* 当前时间 */}
        <div className="status-item current-time">
          <i className="icon icon-clock" />
          <span className="status-text">{formatTime(currentTime)}</span>
        </div>

        {/* 设置按钮 */}
        <div className="status-item settings">
          <button 
            className="settings-button"
            title="设置"
          >
            <i className="icon icon-settings" />
          </button>
        </div>
      </div>

      {/* 详细信息弹窗 */}
      {showDetails && systemStats && (
        <div className="status-details">
          <div className="details-header">
            <h4>系统详细信息</h4>
            <button 
              className="close-button"
              onClick={() => setShowDetails(false)}
            >
              <i className="icon icon-x" />
            </button>
          </div>
          
          <div className="details-content">
            <div className="detail-section">
              <h5>Token 使用情况</h5>
              <div className="detail-item">
                <span className="detail-label">已使用:</span>
                <span className="detail-value">{systemStats.tokenUsage.toLocaleString()}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">总限制:</span>
                <span className="detail-value">{systemStats.maxTokens.toLocaleString()}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">使用率:</span>
                <span className="detail-value">{tokenUsagePercent.toFixed(1)}%</span>
              </div>
            </div>

            <div className="detail-section">
              <h5>性能指标</h5>
              <div className="detail-item">
                <span className="detail-label">平均响应时间:</span>
                <span className="detail-value">{systemStats.responseTime}ms</span>
              </div>
              {systemStats.ragStats && (
                <>
                  <div className="detail-item">
                    <span className="detail-label">搜索缓存命中率:</span>
                    <span className="detail-value">
                      {((systemStats.ragStats.searchStats?.cacheHitRate || 0) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">索引大小:</span>
                    <span className="detail-value">
                      {((systemStats.ragStats.indexStats?.indexSize || 0) / 1024).toFixed(2)} KB
                    </span>
                  </div>
                </>
              )}
            </div>

            {systemStats.ragStats && (
              <div className="detail-section">
                <h5>RAG 系统</h5>
                <div className="detail-item">
                  <span className="detail-label">代码索引项:</span>
                  <span className="detail-value">
                    {(systemStats.ragStats.indexStats?.totalItems || 0).toLocaleString()}
                  </span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">知识库条目:</span>
                  <span className="detail-value">
                    {(systemStats.ragStats.knowledgeStats?.totalItems || 0).toLocaleString()}
                  </span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">总查询数:</span>
                  <span className="detail-value">
                    {(systemStats.ragStats.searchStats?.totalQueries || 0).toLocaleString()}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 详细信息背景遮罩 */}
      {showDetails && (
        <div 
          className="details-overlay"
          onClick={() => setShowDetails(false)}
        />
      )}
    </div>
  );
};

// 导出类型
export type { StatusBarProps };
