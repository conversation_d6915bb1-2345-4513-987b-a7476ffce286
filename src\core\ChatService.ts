/**
 * Chat Service - 聊天服务
 * 
 * 负责处理用户聊天请求，整合LLM和RAG功能
 */

import { EventBus } from './EventBus';
import { ConfigManager } from './ConfigManager';
import { ContextManager } from './ContextManager';
import { CodeContextExtractor } from './CodeContextExtractor';
import { EnhancedCodeContextExtractor } from './EnhancedCodeContextExtractor';
import { ContextPriorityManager } from './ContextPriorityManager';
import { IncrementalContextUpdater } from './IncrementalContextUpdater';
import { RAGSystem } from './RAGSystem';
import { WorkspaceAwareness } from './WorkspaceAwareness';
import { ProgrammingTools } from './ProgrammingTools';
import { LLMManager } from '@/llm/LLMManager';
import { OpenAIProvider } from '@/llm/providers/OpenAIProvider';
import { Message, LLMProvider, ToolCall, ToolResult } from '@/types';
import { LLMRequestConfig, LLMResponse, LLMStreamChunk } from '@/llm/interfaces';

export interface ChatRequest {
  message: string;
  context?: string[];
  stream?: boolean;
  includeContext?: boolean;
}

export interface ChatResponse {
  id: string;
  content: string;
  toolCalls?: ToolCall[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  sources?: string[];
  model: string;
}

export class ChatService {
  private eventBus: EventBus;
  private configManager: ConfigManager;
  private contextManager: ContextManager;
  private codeContextExtractor: CodeContextExtractor;
  private enhancedCodeContextExtractor: EnhancedCodeContextExtractor;
  private contextPriorityManager: ContextPriorityManager;
  private incrementalContextUpdater: IncrementalContextUpdater;
  private ragSystem: RAGSystem;
  private workspaceAwareness: WorkspaceAwareness;
  private programmingTools: ProgrammingTools;
  private llmManager: LLMManager;
  private conversationHistory: Message[] = [];
  private isInitialized = false;

  constructor(eventBus: EventBus, configManager: ConfigManager) {
    this.eventBus = eventBus;
    this.configManager = configManager;
    this.contextManager = new ContextManager(eventBus, {
      maxContextTokens: 64000, // DeepSeek R1的上下文窗口
      reservedTokens: 8192,    // 为响应预留更多token
      compressionThreshold: 0.75
    });
    this.codeContextExtractor = new CodeContextExtractor(eventBus);
    this.enhancedCodeContextExtractor = new EnhancedCodeContextExtractor(eventBus);
    this.contextPriorityManager = new ContextPriorityManager(eventBus);
    this.incrementalContextUpdater = new IncrementalContextUpdater(eventBus, this.contextPriorityManager);
    this.ragSystem = new RAGSystem(eventBus);
    this.workspaceAwareness = new WorkspaceAwareness(eventBus);
    this.programmingTools = new ProgrammingTools(
      eventBus,
      this.codeContextExtractor,
      this.workspaceAwareness
    );
    this.llmManager = new LLMManager(eventBus);

    this.setupEventHandlers();
  }

  /**
   * 初始化聊天服务
   */
  async initialize(): Promise<void> {
    try {
      // 检查配置
      const validation = this.configManager.validateConfig();

      // 如果没有API密钥，只是标记为未完全初始化，但不抛出错误
      if (!validation.isValid) {
        console.log('ChatService partially initialized - configuration needed:', validation.errors);

        await this.eventBus.emit({
          type: 'chat.service_needs_config',
          source: 'ChatService',
          errors: validation.errors,
        });

        // 部分初始化，允许显示配置提示
        this.isInitialized = false;
        return;
      }

      // 初始化LLM提供者
      await this.initializeLLMProviders();

      // 启动增量上下文更新器
      await this.incrementalContextUpdater.start();

      // 初始化RAG系统
      await this.ragSystem.initialize();

      this.isInitialized = true;

      await this.eventBus.emit({
        type: 'chat.service_initialized',
        source: 'ChatService',
      });

      console.log('ChatService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize ChatService:', error);

      // 即使初始化失败，也不抛出错误，而是记录状态
      this.isInitialized = false;

      await this.eventBus.emit({
        type: 'chat.service_init_failed',
        source: 'ChatService',
        error: (error as Error).message,
      });
    }
  }

  /**
   * 发送聊天消息
   */
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    if (!this.isInitialized) {
      throw new Error('ChatService not initialized');
    }

    try {
      // 创建用户消息
      const userMessage: Message = {
        id: this.generateId(),
        type: 'user',
        content: request.message,
        timestamp: Date.now(),
      };

      // 添加到对话历史
      this.conversationHistory.push(userMessage);

      // 发布消息发送事件
      await this.eventBus.emit({
        type: 'chat.message_sent',
        source: 'ChatService',
        message: userMessage,
      });

      // 准备LLM请求
      const config = this.configManager.getFullConfig();
      const llmConfig: LLMRequestConfig = {
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        stream: false,
      };

      // 构建消息历史（包含系统提示）
      const messages = await this.buildMessageHistory(request);

      // 调用LLM（统一使用openai provider，因为DeepSeek使用OpenAI兼容接口）
      const response = await this.llmManager.chat(
        'openai',
        messages,
        llmConfig
      );

      // 创建助手消息
      const assistantMessage: Message = {
        id: response.id,
        type: 'assistant',
        content: response.content,
        timestamp: Date.now(),
        metadata: {
          model: response.model,
          tokens: response.usage?.totalTokens,
        },
        toolCalls: response.toolCalls,
      };

      // 添加到对话历史
      this.conversationHistory.push(assistantMessage);

      // 发布响应事件
      await this.eventBus.emit({
        type: 'chat.response_received',
        source: 'ChatService',
        message: assistantMessage,
        usage: response.usage,
      });

      // 构建响应
      const chatResponse: ChatResponse = {
        id: response.id,
        content: response.content,
        toolCalls: response.toolCalls,
        usage: response.usage,
        model: response.model,
        sources: request.context,
      };

      return chatResponse;
    } catch (error) {
      await this.eventBus.emit({
        type: 'chat.error',
        source: 'ChatService',
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * 发送流式聊天消息
   */
  async* sendMessageStream(request: ChatRequest): AsyncIterable<LLMStreamChunk> {
    if (!this.isInitialized) {
      throw new Error('ChatService not initialized');
    }

    try {
      // 创建用户消息
      const userMessage: Message = {
        id: this.generateId(),
        type: 'user',
        content: request.message,
        timestamp: Date.now(),
      };

      // 添加到对话历史
      this.conversationHistory.push(userMessage);

      // 发布流式开始事件
      await this.eventBus.emit({
        type: 'chat.stream_started',
        source: 'ChatService',
        message: userMessage,
      });

      // 准备LLM请求
      const config = this.configManager.getFullConfig();
      const llmConfig: LLMRequestConfig = {
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        stream: true,
      };

      // 构建消息历史
      const messages = await this.buildMessageHistory(request);

      let fullContent = '';
      let responseId = '';

      // 流式调用LLM（统一使用openai provider）
      for await (const chunk of this.llmManager.chatStream(
        'openai',
        messages,
        llmConfig
      )) {
        if (chunk.id) {
          responseId = chunk.id;
        }
        
        if (chunk.delta.content) {
          fullContent += chunk.delta.content;
        }

        // 发布流式块事件
        await this.eventBus.emit({
          type: 'chat.stream_chunk',
          source: 'ChatService',
          chunkId: chunk.id,
          content: chunk.delta.content,
          hasToolCalls: !!chunk.delta.toolCalls,
        });

        yield chunk;
      }

      // 创建完整的助手消息
      const assistantMessage: Message = {
        id: responseId || this.generateId(),
        type: 'assistant',
        content: fullContent,
        timestamp: Date.now(),
        metadata: {
          model: config.model,
        },
      };

      // 添加到对话历史
      this.conversationHistory.push(assistantMessage);

      // 发布流式完成事件
      await this.eventBus.emit({
        type: 'chat.stream_completed',
        source: 'ChatService',
        message: assistantMessage,
      });

    } catch (error) {
      await this.eventBus.emit({
        type: 'chat.stream_error',
        source: 'ChatService',
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * 获取对话历史
   */
  getConversationHistory(): Message[] {
    return [...this.conversationHistory];
  }

  /**
   * 清除对话历史
   */
  clearConversationHistory(): void {
    this.conversationHistory = [];
    this.eventBus.emit({
      type: 'chat.history_cleared',
      source: 'ChatService',
    });
  }

  /**
   * 重新生成最后一个响应
   */
  async regenerateLastResponse(): Promise<ChatResponse> {
    if (this.conversationHistory.length < 2) {
      throw new Error('No previous message to regenerate');
    }

    // 移除最后一个助手响应
    const lastMessage = this.conversationHistory.pop();
    if (lastMessage?.type !== 'assistant') {
      // 如果最后一条不是助手消息，放回去
      if (lastMessage) {
        this.conversationHistory.push(lastMessage);
      }
      throw new Error('Last message is not from assistant');
    }

    // 获取用户的最后一条消息
    const userMessage = this.conversationHistory[this.conversationHistory.length - 1];
    if (!userMessage || userMessage.type !== 'user') {
      throw new Error('Cannot find user message to regenerate response');
    }

    // 重新发送请求
    return this.sendMessage({
      message: userMessage.content as string,
      stream: false,
    });
  }

  /**
   * 初始化LLM提供者
   */
  private async initializeLLMProviders(): Promise<void> {
    const config = this.configManager.getFullConfig();

    // 清除现有的提供者
    this.llmManager.dispose();

    // 初始化OpenAI兼容提供者（支持OpenAI和DeepSeek）
    if (config.apiKey) {
      const openaiProvider = new OpenAIProvider({
        apiKey: config.apiKey,
        baseUrl: config.baseUrl,
        timeout: 60000,
      });

      // 验证配置
      try {
        const isValid = await openaiProvider.validateConfig();
        if (!isValid) {
          throw new Error('API configuration validation failed');
        }

        this.llmManager.registerProvider(openaiProvider);

        console.log(`LLM Provider registered: ${openaiProvider.name} with base URL: ${config.baseUrl}`);
      } catch (error) {
        console.error('Provider validation failed:', error);
        throw new Error(`Failed to validate API configuration: ${(error as Error).message}`);
      }
    }
  }

  /**
   * 构建消息历史
   */
  private async buildMessageHistory(request: ChatRequest): Promise<Message[]> {
    const messages: Message[] = [];

    // 添加系统提示
    const systemMessage: Message = {
      id: this.generateId(),
      type: 'system',
      content: await this.getEnhancedSystemPrompt(),
      timestamp: Date.now(),
    };
    messages.push(systemMessage);

    // 添加对话历史并进行上下文管理
    const historyWithSystem = [systemMessage, ...this.conversationHistory];
    const managedHistory = await this.contextManager.manageContext(historyWithSystem);

    // 移除系统消息（因为已经添加了）
    const managedConversationHistory = managedHistory.filter(msg =>
      msg.id !== systemMessage.id && msg.type !== 'system'
    );

    messages.push(...managedConversationHistory);

    // 智能添加代码上下文
    if (request.includeContext) {
      const codeContext = await this.extractRelevantCodeContext(request.message);
      if (codeContext) {
        const contextMessage: Message = {
          id: this.generateId(),
          type: 'system',
          content: codeContext,
          timestamp: Date.now(),
        };
        messages.push(contextMessage);
      }
    }

    // 添加手动指定的上下文信息
    if (request.context && request.context.length > 0) {
      const contextMessage: Message = {
        id: this.generateId(),
        type: 'system',
        content: `当前上下文信息：\n${request.context.join('\n')}`,
        timestamp: Date.now(),
      };
      messages.push(contextMessage);
    }

    // 记录上下文统计
    const stats = this.contextManager.getContextStats(messages);
    console.log('Context stats:', stats);

    return messages;
  }

  /**
   * 获取增强的系统提示
   */
  private async getEnhancedSystemPrompt(): Promise<string> {
    let systemPrompt = `# AI Agent - 专业编程助手

你是AI Agent，一个基于DeepSeek R1推理模型的专业编程助手。你具备强大的逻辑推理能力和深度编程知识。

## 核心能力
- **代码理解与生成**：分析、编写、优化各种编程语言的代码
- **问题解决**：运用逻辑推理解决复杂的编程问题
- **架构设计**：提供系统设计和架构建议
- **调试协助**：帮助定位和修复代码问题
- **最佳实践**：分享行业标准和最佳实践
- **工具集成**：提供代码重构、测试生成、格式化等工具支持

## 工作原则
1. **深度思考**：充分利用推理能力，提供深入的分析
2. **准确性优先**：确保技术信息的准确性，不确定时明确说明
3. **实用性导向**：提供可执行的解决方案和具体的代码示例
4. **安全意识**：始终考虑代码的安全性和健壮性
5. **性能考量**：关注代码的性能和可维护性
6. **上下文感知**：充分利用项目和代码上下文信息

## 响应格式
- 使用清晰的结构化回答
- 提供代码时使用适当的语法高亮
- 包含必要的解释和注释
- 在复杂问题上展示推理过程
- 根据项目类型调整建议的深度和方向

## 可用工具
当用户需要时，你可以建议使用以下工具：
- **代码重构**：优化代码结构和可读性
- **测试生成**：为代码生成单元测试
- **代码解释**：详细解释代码逻辑和功能
- **格式化**：统一代码格式
- **优化建议**：提供性能和质量改进建议

当前时间：${new Date().toISOString()}
模型：DeepSeek R1 (推理模型)`;

    // 添加项目上下文信息
    try {
      const projectInfo = this.workspaceAwareness.getProjectInfo();
      if (projectInfo) {
        systemPrompt += `\n\n## 当前项目信息\n${this.workspaceAwareness.getProjectContextSummary()}`;
      }
    } catch (error) {
      console.log('Failed to get project context for system prompt:', error);
    }

    // 添加RAG系统信息
    try {
      const ragStats = this.ragSystem.getStats();
      systemPrompt += `\n\n## 智能检索系统\n- 代码索引: ${ragStats.indexStats.totalItems} 项\n- 知识库: ${ragStats.knowledgeStats.totalItems} 条目\n- 支持语义搜索、关键词匹配和混合检索策略`;
    } catch (error) {
      console.log('Failed to get RAG stats for system prompt:', error);
    }

    return systemPrompt;
  }

  /**
   * 提取相关的代码上下文（增强版）
   */
  private async extractRelevantCodeContext(message: string): Promise<string | null> {
    try {
      // 分析消息内容，判断是否需要代码上下文
      const needsContext = this.shouldIncludeCodeContext(message);
      if (!needsContext) {
        return null;
      }

      // 使用RAG系统进行智能检索
      let ragResults = null;
      try {
        ragResults = await this.ragSystem.query({
          text: message,
          includeCode: true,
          includeKnowledge: true,
          searchType: 'hybrid',
          limit: 3
        });
      } catch (error) {
        console.warn('RAG query failed, falling back to enhanced context:', error);
      }

      // 使用增强的上下文提取器作为补充
      const enhancedContexts = await this.enhancedCodeContextExtractor.getEnhancedContext(
        undefined, // targetFile
        undefined, // targetRange
        {
          includeGitHistory: true,
          includeDependencies: true,
          includeCallGraph: true,
          maxDepth: 2,
          maxFiles: ragResults ? 2 : 5, // 如果有RAG结果，减少上下文数量
          priorityThreshold: 0.3
        }
      );

      if (enhancedContexts.length === 0 && (!ragResults || (ragResults.codeResults.length === 0 && ragResults.knowledgeResults.length === 0))) {
        return null;
      }

      let contextParts: string[] = [];

      // 添加RAG检索结果
      if (ragResults) {
        // 添加代码检索结果
        if (ragResults.codeResults.length > 0) {
          contextParts.push(`## 🔍 智能代码检索结果\n${ragResults.explanation}\n`);

          for (const result of ragResults.codeResults.slice(0, 2)) {
            let codeSection = `### ${this.getContextTypeLabel(result.item.type)} - ${result.item.metadata.name || path.basename(result.item.filePath)}`;
            codeSection += `\n📍 ${result.item.filePath}`;
            if (result.item.metadata.lineStart) {
              codeSection += `:${result.item.metadata.lineStart}-${result.item.metadata.lineEnd}`;
            }
            codeSection += `\n🎯 ${result.explanation}`;
            codeSection += `\n\n\`\`\`${result.item.metadata.language}\n${result.item.content}\n\`\`\``;
            contextParts.push(codeSection);
          }
        }

        // 添加知识库检索结果
        if (ragResults.knowledgeResults.length > 0) {
          contextParts.push(`## 📚 相关知识库内容\n`);

          for (const knowledge of ragResults.knowledgeResults.slice(0, 2)) {
            let knowledgeSection = `### ${knowledge.title}`;
            knowledgeSection += `\n🏷️ ${knowledge.tags.join(', ')}`;
            knowledgeSection += `\n⭐ 评分: ${knowledge.rating}/5`;
            if (knowledge.url) {
              knowledgeSection += `\n🔗 [查看原文](${knowledge.url})`;
            }
            knowledgeSection += `\n\n${knowledge.content.length > 500 ? knowledge.content.substring(0, 500) + '...' : knowledge.content}`;
            contextParts.push(knowledgeSection);
          }
        }

        // 添加智能建议
        if (ragResults.suggestions.length > 0) {
          contextParts.push(`## 💡 相关建议\n${ragResults.suggestions.map(s => `- ${s}`).join('\n')}`);
        }
      }

      // 添加增强上下文作为补充
      if (enhancedContexts.length > 0) {
        const recommendedContexts = this.contextPriorityManager.getRecommendedContexts(
          enhancedContexts,
          enhancedContexts[0],
          message,
          ragResults ? 1 : 2 // 如果有RAG结果，减少上下文数量
        );

        if (recommendedContexts.length > 0) {
          contextParts.push(`## 📝 当前代码上下文\n`);

          for (const context of recommendedContexts) {
            let contextSection = `### ${this.getContextTypeLabel(context.type)} - ${path.basename(context.filePath)}`;
            contextSection += `\n📍 ${context.filePath}`;

            if (context.lineRange) {
              contextSection += `:${context.lineRange.start}-${context.lineRange.end}`;
            }

            contextSection += `\n🎯 优先级: ${(context.priority * 100).toFixed(1)}% | 相关性: ${(context.relevanceScore * 100).toFixed(1)}%`;

            if (context.gitContext) {
              contextSection += `\n📅 最后修改: ${context.gitContext.lastModified.toLocaleDateString()} by ${context.gitContext.author}`;
            }

            contextSection += `\n\n\`\`\`${context.language}\n${context.content}\n\`\`\``;
            contextParts.push(contextSection);
          }
        }
      }

      return contextParts.length > 0 ? `# 🧠 AI智能上下文分析\n\n${contextParts.join('\n\n')}` : null;

    } catch (error) {
      console.error('Failed to extract enhanced code context:', error);
      // 降级到基础上下文提取
      return this.extractBasicCodeContext(message);
    }
  }

  /**
   * 基础代码上下文提取（降级方案）
   */
  private async extractBasicCodeContext(message: string): Promise<string | null> {
    try {
      let contextParts: string[] = [];

      // 获取选中的代码（如果有）
      const selectionContext = await this.codeContextExtractor.getSelectionContext();
      if (selectionContext) {
        contextParts.push(`## 选中代码 (${selectionContext.filePath}:${selectionContext.lineRange?.start}-${selectionContext.lineRange?.end})
\`\`\`${selectionContext.language}
${selectionContext.content}
\`\`\``);
      }

      // 获取当前文件上下文（如果没有选中代码）
      if (!selectionContext) {
        const fileContext = await this.codeContextExtractor.getFileContext({
          maxFileSize: 10000
        });
        if (fileContext) {
          contextParts.push(`## 当前文件 (${fileContext.filePath})
\`\`\`${fileContext.language}
${fileContext.content.length > 5000 ? fileContext.content.substring(0, 5000) + '\n// ... 文件内容已截断' : fileContext.content}
\`\`\``);
        }
      }

      return contextParts.length > 0 ? `# 代码上下文\n\n${contextParts.join('\n\n')}` : null;

    } catch (error) {
      console.error('Failed to extract basic code context:', error);
      return null;
    }
  }

  /**
   * 判断是否应该包含代码上下文
   */
  private shouldIncludeCodeContext(message: string): boolean {
    const contextKeywords = [
      '这段代码', '当前代码', '这个函数', '这个类', '这个文件',
      '重构', '优化', '解释', '分析', '修复', '调试',
      '测试', '单元测试', '集成测试',
      '性能', '安全', '最佳实践',
      'bug', 'error', 'issue', 'problem'
    ];

    const lowerMessage = message.toLowerCase();
    return contextKeywords.some(keyword => lowerMessage.includes(keyword.toLowerCase()));
  }

  /**
   * 判断是否应该包含相关文件
   */
  private shouldIncludeRelatedFiles(message: string): boolean {
    const relatedKeywords = [
      '相关文件', '依赖', '导入', 'import', 'require',
      '整个项目', '项目结构', '架构',
      '重构整个', '全局搜索'
    ];

    const lowerMessage = message.toLowerCase();
    return relatedKeywords.some(keyword => lowerMessage.includes(keyword.toLowerCase()));
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 监听配置更新
    this.eventBus.subscribe('config.updated', async () => {
      try {
        await this.initializeLLMProviders();
      } catch (error) {
        console.error('Failed to reinitialize LLM providers after config update:', error);
      }
    });

    // 监听API密钥更新
    this.eventBus.subscribe('config.api_key_updated', async () => {
      try {
        await this.initializeLLMProviders();
      } catch (error) {
        console.error('Failed to reinitialize LLM providers after API key update:', error);
      }
    });
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 检查服务是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 获取编程工具实例
   */
  getProgrammingTools(): ProgrammingTools {
    return this.programmingTools;
  }

  /**
   * 获取代码上下文提取器
   */
  getCodeContextExtractor(): CodeContextExtractor {
    return this.codeContextExtractor;
  }

  /**
   * 获取工作区感知器
   */
  getWorkspaceAwareness(): WorkspaceAwareness {
    return this.workspaceAwareness;
  }

  /**
   * 分析工作区（如果尚未分析）
   */
  async ensureWorkspaceAnalyzed(): Promise<void> {
    if (!this.workspaceAwareness.getProjectInfo()) {
      try {
        await this.workspaceAwareness.analyzeWorkspace();
      } catch (error) {
        console.error('Failed to analyze workspace:', error);
      }
    }
  }

  /**
   * 获取上下文类型标签
   */
  private getContextTypeLabel(type: string): string {
    const labels = {
      'selection': '选中代码',
      'file': '当前文件',
      'related': '相关文件',
      'workspace': '工作区'
    };
    return labels[type] || type;
  }

  /**
   * 获取增强的上下文提取器
   */
  getEnhancedCodeContextExtractor(): EnhancedCodeContextExtractor {
    return this.enhancedCodeContextExtractor;
  }

  /**
   * 获取上下文优先级管理器
   */
  getContextPriorityManager(): ContextPriorityManager {
    return this.contextPriorityManager;
  }

  /**
   * 获取增量上下文更新器
   */
  getIncrementalContextUpdater(): IncrementalContextUpdater {
    return this.incrementalContextUpdater;
  }

  /**
   * 获取RAG系统
   */
  getRAGSystem(): RAGSystem {
    return this.ragSystem;
  }

  /**
   * 手动触发上下文更新
   */
  async triggerContextUpdate(filePath?: string): Promise<void> {
    if (filePath) {
      await this.incrementalContextUpdater.triggerUpdate(filePath);
    } else {
      // 触发当前活动文件的更新
      const activeEditor = vscode.window.activeTextEditor;
      if (activeEditor) {
        await this.incrementalContextUpdater.triggerUpdate(activeEditor.document.uri.fsPath);
      }
    }
  }

  /**
   * 获取上下文更新统计
   */
  getContextUpdateStats(): any {
    return this.incrementalContextUpdater.getUpdateStats();
  }

  /**
   * 执行RAG查询
   */
  async performRAGQuery(query: string, options?: {
    includeCode?: boolean;
    includeKnowledge?: boolean;
    searchType?: 'semantic' | 'keyword' | 'hybrid';
    limit?: number;
  }): Promise<any> {
    if (!this.isInitialized) {
      throw new Error('ChatService not initialized');
    }

    return await this.ragSystem.query({
      text: query,
      includeCode: options?.includeCode ?? true,
      includeKnowledge: options?.includeKnowledge ?? true,
      searchType: options?.searchType ?? 'hybrid',
      limit: options?.limit ?? 5
    });
  }

  /**
   * 重建RAG索引
   */
  async rebuildRAGIndex(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('ChatService not initialized');
    }

    await this.ragSystem.rebuildIndex();
  }

  /**
   * 同步知识库
   */
  async syncKnowledgeBase(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('ChatService not initialized');
    }

    await this.ragSystem.syncKnowledgeBase();
  }

  /**
   * 获取RAG系统统计信息
   */
  getRAGStats(): any {
    if (!this.isInitialized) {
      return null;
    }

    return this.ragSystem.getStats();
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.conversationHistory = [];
    this.llmManager.dispose();
    this.incrementalContextUpdater.dispose();
    this.enhancedCodeContextExtractor.dispose();
    this.ragSystem.dispose();
  }
}
