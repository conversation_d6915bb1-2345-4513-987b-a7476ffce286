/**
 * Chat Service - 聊天服务
 * 
 * 负责处理用户聊天请求，整合LLM和RAG功能
 */

import { EventBus } from './EventBus';
import { ConfigManager } from './ConfigManager';
import { LLMManager } from '@/llm/LLMManager';
import { OpenAIProvider } from '@/llm/providers/OpenAIProvider';
import { Message, LLMProvider, ToolCall, ToolResult } from '@/types';
import { LLMRequestConfig, LLMResponse, LLMStreamChunk } from '@/llm/interfaces';

export interface ChatRequest {
  message: string;
  context?: string[];
  stream?: boolean;
  includeContext?: boolean;
}

export interface ChatResponse {
  id: string;
  content: string;
  toolCalls?: ToolCall[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  sources?: string[];
  model: string;
}

export class ChatService {
  private eventBus: EventBus;
  private configManager: ConfigManager;
  private llmManager: LLMManager;
  private conversationHistory: Message[] = [];
  private isInitialized = false;

  constructor(eventBus: EventBus, configManager: ConfigManager) {
    this.eventBus = eventBus;
    this.configManager = configManager;
    this.llmManager = new LLMManager(eventBus);
    
    this.setupEventHandlers();
  }

  /**
   * 初始化聊天服务
   */
  async initialize(): Promise<void> {
    try {
      // 检查配置
      const validation = this.configManager.validateConfig();

      // 如果没有API密钥，只是标记为未完全初始化，但不抛出错误
      if (!validation.isValid) {
        console.log('ChatService partially initialized - configuration needed:', validation.errors);

        await this.eventBus.emit({
          type: 'chat.service_needs_config',
          source: 'ChatService',
          errors: validation.errors,
        });

        // 部分初始化，允许显示配置提示
        this.isInitialized = false;
        return;
      }

      // 初始化LLM提供者
      await this.initializeLLMProviders();

      this.isInitialized = true;

      await this.eventBus.emit({
        type: 'chat.service_initialized',
        source: 'ChatService',
      });

      console.log('ChatService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize ChatService:', error);

      // 即使初始化失败，也不抛出错误，而是记录状态
      this.isInitialized = false;

      await this.eventBus.emit({
        type: 'chat.service_init_failed',
        source: 'ChatService',
        error: (error as Error).message,
      });
    }
  }

  /**
   * 发送聊天消息
   */
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    if (!this.isInitialized) {
      throw new Error('ChatService not initialized');
    }

    try {
      // 创建用户消息
      const userMessage: Message = {
        id: this.generateId(),
        type: 'user',
        content: request.message,
        timestamp: Date.now(),
      };

      // 添加到对话历史
      this.conversationHistory.push(userMessage);

      // 发布消息发送事件
      await this.eventBus.emit({
        type: 'chat.message_sent',
        source: 'ChatService',
        message: userMessage,
      });

      // 准备LLM请求
      const config = this.configManager.getFullConfig();
      const llmConfig: LLMRequestConfig = {
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        stream: false,
      };

      // 构建消息历史（包含系统提示）
      const messages = this.buildMessageHistory(request);

      // 调用LLM
      const response = await this.llmManager.chat(
        config.provider,
        messages,
        llmConfig
      );

      // 创建助手消息
      const assistantMessage: Message = {
        id: response.id,
        type: 'assistant',
        content: response.content,
        timestamp: Date.now(),
        metadata: {
          model: response.model,
          tokens: response.usage?.totalTokens,
        },
        toolCalls: response.toolCalls,
      };

      // 添加到对话历史
      this.conversationHistory.push(assistantMessage);

      // 发布响应事件
      await this.eventBus.emit({
        type: 'chat.response_received',
        source: 'ChatService',
        message: assistantMessage,
        usage: response.usage,
      });

      // 构建响应
      const chatResponse: ChatResponse = {
        id: response.id,
        content: response.content,
        toolCalls: response.toolCalls,
        usage: response.usage,
        model: response.model,
        sources: request.context,
      };

      return chatResponse;
    } catch (error) {
      await this.eventBus.emit({
        type: 'chat.error',
        source: 'ChatService',
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * 发送流式聊天消息
   */
  async* sendMessageStream(request: ChatRequest): AsyncIterable<LLMStreamChunk> {
    if (!this.isInitialized) {
      throw new Error('ChatService not initialized');
    }

    try {
      // 创建用户消息
      const userMessage: Message = {
        id: this.generateId(),
        type: 'user',
        content: request.message,
        timestamp: Date.now(),
      };

      // 添加到对话历史
      this.conversationHistory.push(userMessage);

      // 发布流式开始事件
      await this.eventBus.emit({
        type: 'chat.stream_started',
        source: 'ChatService',
        message: userMessage,
      });

      // 准备LLM请求
      const config = this.configManager.getFullConfig();
      const llmConfig: LLMRequestConfig = {
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        stream: true,
      };

      // 构建消息历史
      const messages = this.buildMessageHistory(request);

      let fullContent = '';
      let responseId = '';

      // 流式调用LLM
      for await (const chunk of this.llmManager.chatStream(
        config.provider,
        messages,
        llmConfig
      )) {
        if (chunk.id) {
          responseId = chunk.id;
        }
        
        if (chunk.delta.content) {
          fullContent += chunk.delta.content;
        }

        // 发布流式块事件
        await this.eventBus.emit({
          type: 'chat.stream_chunk',
          source: 'ChatService',
          chunkId: chunk.id,
          content: chunk.delta.content,
          hasToolCalls: !!chunk.delta.toolCalls,
        });

        yield chunk;
      }

      // 创建完整的助手消息
      const assistantMessage: Message = {
        id: responseId || this.generateId(),
        type: 'assistant',
        content: fullContent,
        timestamp: Date.now(),
        metadata: {
          model: config.model,
        },
      };

      // 添加到对话历史
      this.conversationHistory.push(assistantMessage);

      // 发布流式完成事件
      await this.eventBus.emit({
        type: 'chat.stream_completed',
        source: 'ChatService',
        message: assistantMessage,
      });

    } catch (error) {
      await this.eventBus.emit({
        type: 'chat.stream_error',
        source: 'ChatService',
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * 获取对话历史
   */
  getConversationHistory(): Message[] {
    return [...this.conversationHistory];
  }

  /**
   * 清除对话历史
   */
  clearConversationHistory(): void {
    this.conversationHistory = [];
    this.eventBus.emit({
      type: 'chat.history_cleared',
      source: 'ChatService',
    });
  }

  /**
   * 重新生成最后一个响应
   */
  async regenerateLastResponse(): Promise<ChatResponse> {
    if (this.conversationHistory.length < 2) {
      throw new Error('No previous message to regenerate');
    }

    // 移除最后一个助手响应
    const lastMessage = this.conversationHistory.pop();
    if (lastMessage?.type !== 'assistant') {
      // 如果最后一条不是助手消息，放回去
      if (lastMessage) {
        this.conversationHistory.push(lastMessage);
      }
      throw new Error('Last message is not from assistant');
    }

    // 获取用户的最后一条消息
    const userMessage = this.conversationHistory[this.conversationHistory.length - 1];
    if (!userMessage || userMessage.type !== 'user') {
      throw new Error('Cannot find user message to regenerate response');
    }

    // 重新发送请求
    return this.sendMessage({
      message: userMessage.content as string,
      stream: false,
    });
  }

  /**
   * 初始化LLM提供者
   */
  private async initializeLLMProviders(): Promise<void> {
    const config = this.configManager.getFullConfig();

    // 初始化OpenAI提供者
    if (config.provider === 'openai' && config.apiKey) {
      const openaiProvider = new OpenAIProvider({
        apiKey: config.apiKey,
        baseUrl: config.baseUrl,
        timeout: 60000,
      });

      // 验证配置
      const isValid = await openaiProvider.validateConfig();
      if (!isValid) {
        throw new Error('Invalid OpenAI configuration');
      }

      this.llmManager.registerProvider(openaiProvider);
    }

    // TODO: 添加其他提供者的初始化
  }

  /**
   * 构建消息历史
   */
  private buildMessageHistory(request: ChatRequest): Message[] {
    const messages: Message[] = [];

    // 添加系统提示
    messages.push({
      id: this.generateId(),
      type: 'system',
      content: this.getSystemPrompt(),
      timestamp: Date.now(),
    });

    // 添加对话历史（保留最近的消息）
    const maxHistoryLength = 10; // 保留最近10条消息
    const recentHistory = this.conversationHistory.slice(-maxHistoryLength);
    messages.push(...recentHistory);

    return messages;
  }

  /**
   * 获取系统提示
   */
  private getSystemPrompt(): string {
    return `你是一个专业的AI编程助手，名为AI Agent。你的主要职责是：

1. 帮助开发者理解、编写和优化代码
2. 提供准确的技术建议和最佳实践
3. 解释复杂的编程概念和算法
4. 协助调试和解决编程问题
5. 生成高质量的代码和文档

请始终：
- 提供准确、有用的信息
- 使用清晰、专业的语言
- 在适当时提供代码示例
- 考虑性能、安全性和可维护性
- 承认不确定性，不要编造信息

当前时间：${new Date().toISOString()}`;
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 监听配置更新
    this.eventBus.subscribe('config.updated', async () => {
      try {
        await this.initializeLLMProviders();
      } catch (error) {
        console.error('Failed to reinitialize LLM providers after config update:', error);
      }
    });

    // 监听API密钥更新
    this.eventBus.subscribe('config.api_key_updated', async () => {
      try {
        await this.initializeLLMProviders();
      } catch (error) {
        console.error('Failed to reinitialize LLM providers after API key update:', error);
      }
    });
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 检查服务是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.conversationHistory = [];
    this.llmManager.dispose();
  }
}
