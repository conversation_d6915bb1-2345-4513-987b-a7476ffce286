/**
 * Chat Service - 聊天服务
 * 
 * 负责处理用户聊天请求，整合LLM和RAG功能
 */

import { EventBus } from './EventBus';
import { ConfigManager } from './ConfigManager';
import { ContextManager } from './ContextManager';
import { LLMManager } from '@/llm/LLMManager';
import { OpenAIProvider } from '@/llm/providers/OpenAIProvider';
import { Message, LLMProvider, ToolCall, ToolResult } from '@/types';
import { LLMRequestConfig, LLMResponse, LLMStreamChunk } from '@/llm/interfaces';

export interface ChatRequest {
  message: string;
  context?: string[];
  stream?: boolean;
  includeContext?: boolean;
}

export interface ChatResponse {
  id: string;
  content: string;
  toolCalls?: ToolCall[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  sources?: string[];
  model: string;
}

export class ChatService {
  private eventBus: EventBus;
  private configManager: ConfigManager;
  private contextManager: ContextManager;
  private llmManager: LLMManager;
  private conversationHistory: Message[] = [];
  private isInitialized = false;

  constructor(eventBus: EventBus, configManager: ConfigManager) {
    this.eventBus = eventBus;
    this.configManager = configManager;
    this.contextManager = new ContextManager(eventBus, {
      maxContextTokens: 64000, // DeepSeek R1的上下文窗口
      reservedTokens: 8192,    // 为响应预留更多token
      compressionThreshold: 0.75
    });
    this.llmManager = new LLMManager(eventBus);

    this.setupEventHandlers();
  }

  /**
   * 初始化聊天服务
   */
  async initialize(): Promise<void> {
    try {
      // 检查配置
      const validation = this.configManager.validateConfig();

      // 如果没有API密钥，只是标记为未完全初始化，但不抛出错误
      if (!validation.isValid) {
        console.log('ChatService partially initialized - configuration needed:', validation.errors);

        await this.eventBus.emit({
          type: 'chat.service_needs_config',
          source: 'ChatService',
          errors: validation.errors,
        });

        // 部分初始化，允许显示配置提示
        this.isInitialized = false;
        return;
      }

      // 初始化LLM提供者
      await this.initializeLLMProviders();

      this.isInitialized = true;

      await this.eventBus.emit({
        type: 'chat.service_initialized',
        source: 'ChatService',
      });

      console.log('ChatService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize ChatService:', error);

      // 即使初始化失败，也不抛出错误，而是记录状态
      this.isInitialized = false;

      await this.eventBus.emit({
        type: 'chat.service_init_failed',
        source: 'ChatService',
        error: (error as Error).message,
      });
    }
  }

  /**
   * 发送聊天消息
   */
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    if (!this.isInitialized) {
      throw new Error('ChatService not initialized');
    }

    try {
      // 创建用户消息
      const userMessage: Message = {
        id: this.generateId(),
        type: 'user',
        content: request.message,
        timestamp: Date.now(),
      };

      // 添加到对话历史
      this.conversationHistory.push(userMessage);

      // 发布消息发送事件
      await this.eventBus.emit({
        type: 'chat.message_sent',
        source: 'ChatService',
        message: userMessage,
      });

      // 准备LLM请求
      const config = this.configManager.getFullConfig();
      const llmConfig: LLMRequestConfig = {
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        stream: false,
      };

      // 构建消息历史（包含系统提示）
      const messages = await this.buildMessageHistory(request);

      // 调用LLM（统一使用openai provider，因为DeepSeek使用OpenAI兼容接口）
      const response = await this.llmManager.chat(
        'openai',
        messages,
        llmConfig
      );

      // 创建助手消息
      const assistantMessage: Message = {
        id: response.id,
        type: 'assistant',
        content: response.content,
        timestamp: Date.now(),
        metadata: {
          model: response.model,
          tokens: response.usage?.totalTokens,
        },
        toolCalls: response.toolCalls,
      };

      // 添加到对话历史
      this.conversationHistory.push(assistantMessage);

      // 发布响应事件
      await this.eventBus.emit({
        type: 'chat.response_received',
        source: 'ChatService',
        message: assistantMessage,
        usage: response.usage,
      });

      // 构建响应
      const chatResponse: ChatResponse = {
        id: response.id,
        content: response.content,
        toolCalls: response.toolCalls,
        usage: response.usage,
        model: response.model,
        sources: request.context,
      };

      return chatResponse;
    } catch (error) {
      await this.eventBus.emit({
        type: 'chat.error',
        source: 'ChatService',
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * 发送流式聊天消息
   */
  async* sendMessageStream(request: ChatRequest): AsyncIterable<LLMStreamChunk> {
    if (!this.isInitialized) {
      throw new Error('ChatService not initialized');
    }

    try {
      // 创建用户消息
      const userMessage: Message = {
        id: this.generateId(),
        type: 'user',
        content: request.message,
        timestamp: Date.now(),
      };

      // 添加到对话历史
      this.conversationHistory.push(userMessage);

      // 发布流式开始事件
      await this.eventBus.emit({
        type: 'chat.stream_started',
        source: 'ChatService',
        message: userMessage,
      });

      // 准备LLM请求
      const config = this.configManager.getFullConfig();
      const llmConfig: LLMRequestConfig = {
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        stream: true,
      };

      // 构建消息历史
      const messages = await this.buildMessageHistory(request);

      let fullContent = '';
      let responseId = '';

      // 流式调用LLM（统一使用openai provider）
      for await (const chunk of this.llmManager.chatStream(
        'openai',
        messages,
        llmConfig
      )) {
        if (chunk.id) {
          responseId = chunk.id;
        }
        
        if (chunk.delta.content) {
          fullContent += chunk.delta.content;
        }

        // 发布流式块事件
        await this.eventBus.emit({
          type: 'chat.stream_chunk',
          source: 'ChatService',
          chunkId: chunk.id,
          content: chunk.delta.content,
          hasToolCalls: !!chunk.delta.toolCalls,
        });

        yield chunk;
      }

      // 创建完整的助手消息
      const assistantMessage: Message = {
        id: responseId || this.generateId(),
        type: 'assistant',
        content: fullContent,
        timestamp: Date.now(),
        metadata: {
          model: config.model,
        },
      };

      // 添加到对话历史
      this.conversationHistory.push(assistantMessage);

      // 发布流式完成事件
      await this.eventBus.emit({
        type: 'chat.stream_completed',
        source: 'ChatService',
        message: assistantMessage,
      });

    } catch (error) {
      await this.eventBus.emit({
        type: 'chat.stream_error',
        source: 'ChatService',
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * 获取对话历史
   */
  getConversationHistory(): Message[] {
    return [...this.conversationHistory];
  }

  /**
   * 清除对话历史
   */
  clearConversationHistory(): void {
    this.conversationHistory = [];
    this.eventBus.emit({
      type: 'chat.history_cleared',
      source: 'ChatService',
    });
  }

  /**
   * 重新生成最后一个响应
   */
  async regenerateLastResponse(): Promise<ChatResponse> {
    if (this.conversationHistory.length < 2) {
      throw new Error('No previous message to regenerate');
    }

    // 移除最后一个助手响应
    const lastMessage = this.conversationHistory.pop();
    if (lastMessage?.type !== 'assistant') {
      // 如果最后一条不是助手消息，放回去
      if (lastMessage) {
        this.conversationHistory.push(lastMessage);
      }
      throw new Error('Last message is not from assistant');
    }

    // 获取用户的最后一条消息
    const userMessage = this.conversationHistory[this.conversationHistory.length - 1];
    if (!userMessage || userMessage.type !== 'user') {
      throw new Error('Cannot find user message to regenerate response');
    }

    // 重新发送请求
    return this.sendMessage({
      message: userMessage.content as string,
      stream: false,
    });
  }

  /**
   * 初始化LLM提供者
   */
  private async initializeLLMProviders(): Promise<void> {
    const config = this.configManager.getFullConfig();

    // 清除现有的提供者
    this.llmManager.dispose();

    // 初始化OpenAI兼容提供者（支持OpenAI和DeepSeek）
    if (config.apiKey) {
      const openaiProvider = new OpenAIProvider({
        apiKey: config.apiKey,
        baseUrl: config.baseUrl,
        timeout: 60000,
      });

      // 验证配置
      try {
        const isValid = await openaiProvider.validateConfig();
        if (!isValid) {
          throw new Error('API configuration validation failed');
        }

        this.llmManager.registerProvider(openaiProvider);

        console.log(`LLM Provider registered: ${openaiProvider.name} with base URL: ${config.baseUrl}`);
      } catch (error) {
        console.error('Provider validation failed:', error);
        throw new Error(`Failed to validate API configuration: ${(error as Error).message}`);
      }
    }
  }

  /**
   * 构建消息历史
   */
  private async buildMessageHistory(request: ChatRequest): Promise<Message[]> {
    const messages: Message[] = [];

    // 添加系统提示
    const systemMessage: Message = {
      id: this.generateId(),
      type: 'system',
      content: this.getSystemPrompt(),
      timestamp: Date.now(),
    };
    messages.push(systemMessage);

    // 添加对话历史并进行上下文管理
    const historyWithSystem = [systemMessage, ...this.conversationHistory];
    const managedHistory = await this.contextManager.manageContext(historyWithSystem);

    // 移除系统消息（因为已经添加了）
    const managedConversationHistory = managedHistory.filter(msg =>
      msg.id !== systemMessage.id && msg.type !== 'system'
    );

    messages.push(...managedConversationHistory);

    // 添加上下文信息（如果有）
    if (request.context && request.context.length > 0) {
      const contextMessage: Message = {
        id: this.generateId(),
        type: 'system',
        content: `当前上下文信息：\n${request.context.join('\n')}`,
        timestamp: Date.now(),
      };
      messages.push(contextMessage);
    }

    // 记录上下文统计
    const stats = this.contextManager.getContextStats(messages);
    console.log('Context stats:', stats);

    return messages;
  }

  /**
   * 获取系统提示
   */
  private getSystemPrompt(): string {
    return `# AI Agent - 专业编程助手

你是AI Agent，一个基于DeepSeek R1推理模型的专业编程助手。你具备强大的逻辑推理能力和深度编程知识。

## 核心能力
- **代码理解与生成**：分析、编写、优化各种编程语言的代码
- **问题解决**：运用逻辑推理解决复杂的编程问题
- **架构设计**：提供系统设计和架构建议
- **调试协助**：帮助定位和修复代码问题
- **最佳实践**：分享行业标准和最佳实践

## 工作原则
1. **深度思考**：充分利用推理能力，提供深入的分析
2. **准确性优先**：确保技术信息的准确性，不确定时明确说明
3. **实用性导向**：提供可执行的解决方案和具体的代码示例
4. **安全意识**：始终考虑代码的安全性和健壮性
5. **性能考量**：关注代码的性能和可维护性

## 响应格式
- 使用清晰的结构化回答
- 提供代码时使用适当的语法高亮
- 包含必要的解释和注释
- 在复杂问题上展示推理过程

## 上下文感知
- 记住对话历史中的重要信息
- 理解当前开发环境和项目背景
- 根据用户的技能水平调整回答深度

当前时间：${new Date().toISOString()}
模型：DeepSeek R1 (推理模型)`;
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 监听配置更新
    this.eventBus.subscribe('config.updated', async () => {
      try {
        await this.initializeLLMProviders();
      } catch (error) {
        console.error('Failed to reinitialize LLM providers after config update:', error);
      }
    });

    // 监听API密钥更新
    this.eventBus.subscribe('config.api_key_updated', async () => {
      try {
        await this.initializeLLMProviders();
      } catch (error) {
        console.error('Failed to reinitialize LLM providers after API key update:', error);
      }
    });
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 检查服务是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.conversationHistory = [];
    this.llmManager.dispose();
  }
}
