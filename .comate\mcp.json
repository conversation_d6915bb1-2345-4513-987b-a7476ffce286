{"mcpServers": {"maven": {"command": "npx", "args": ["mcp-maven-deps"]}, "gitee": {"command": "npx", "args": ["-y", "@gitee/mcp-gitee@latest"], "env": {"GITEE_API_BASE": "https://gitee.com/api/v5", "GITEE_ACCESS_TOKEN": "<your personal access token>"}}, "everything-search": {"command": "uvx", "args": ["mcp-server-everything-search"]}, "docker": {"command": "uvx", "args": ["docker-mcp"]}, "kubernetes-readonly": {"command": "npx", "args": ["mcp-server-kubernetes"], "env": {"ALLOW_ONLY_NON_DESTRUCTIVE_TOOLS": "true"}}, "mysql": {"command": "uv", "args": ["--directory", "path/to/mysql_mcp_server", "run", "mysql_mcp_server"], "env": {"MYSQL_HOST": "localhost", "MYSQL_PORT": "3306", "MYSQL_USER": "your_username", "MYSQL_PASSWORD": "your_password", "MYSQL_DATABASE": "your_database"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": "6000"}}, "excel": {"command": "uvx", "args": ["excel-mcp-server", "stdio"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "time": {"command": "uvx", "args": ["mcp-server-time"]}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", "path/to/git/repo"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop", "/path/to/other/allowed/dir"]}, "f2c-mcp": {"command": "npx", "args": ["-y", "@f2c/mcp"], "env": {"personalToken": ""}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "<YOUR_TOKEN>"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "/path/to/custom/memory.json"}}}}