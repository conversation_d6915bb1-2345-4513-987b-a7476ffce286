/**
 * Configuration Manager - 配置管理器
 * 
 * 负责管理扩展配置，包括API密钥的安全存储
 */

import * as vscode from 'vscode';
import { EventBus } from './EventBus';
import { ConfigState, LLMProvider } from '@/types';

export interface ConfigManagerOptions {
  secretStorage: vscode.SecretStorage;
  globalState: vscode.Memento;
  workspaceState: vscode.Memento;
}

export class ConfigManager {
  private eventBus: EventBus;
  private secretStorage: vscode.SecretStorage;
  private globalState: vscode.Memento;
  private workspaceState: vscode.Memento;
  private config: vscode.WorkspaceConfiguration;
  private currentConfig: ConfigState;

  constructor(eventBus: EventBus, options: ConfigManagerOptions) {
    this.eventBus = eventBus;
    this.secretStorage = options.secretStorage;
    this.globalState = options.globalState;
    this.workspaceState = options.workspaceState;
    this.config = vscode.workspace.getConfiguration('ai-agent');
    
    // 初始化默认配置
    this.currentConfig = {
      apiKey: '',
      baseUrl: this.config.get('baseUrl', 'https://api.openai.com/v1'),
      model: this.config.get('model', 'gpt-4o'),
      temperature: this.config.get('temperature', 0.2),
      maxTokens: this.config.get('maxTokens', 4096),
      provider: this.config.get('provider', 'openai') as LLMProvider,
    };

    this.setupConfigurationWatcher();
  }

  /**
   * 初始化配置管理器
   */
  async initialize(): Promise<void> {
    try {
      // 从安全存储中加载API密钥
      await this.loadApiKey();
      
      // 发布配置初始化事件
      await this.eventBus.emit({
        type: 'config.initialized',
        source: 'ConfigManager',
        config: this.getSafeConfig(),
      });

      console.log('ConfigManager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize ConfigManager:', error);
      throw error;
    }
  }

  /**
   * 获取当前配置（不包含敏感信息）
   */
  getSafeConfig(): Omit<ConfigState, 'apiKey'> {
    const { apiKey, ...safeConfig } = this.currentConfig;
    return safeConfig;
  }

  /**
   * 获取完整配置（包含API密钥）
   */
  getFullConfig(): ConfigState {
    return { ...this.currentConfig };
  }

  /**
   * 检查API密钥是否已配置
   */
  hasApiKey(): boolean {
    return !!this.currentConfig.apiKey && this.currentConfig.apiKey.length > 0;
  }

  /**
   * 设置API密钥
   */
  async setApiKey(apiKey: string): Promise<void> {
    try {
      // 验证API密钥格式
      if (!this.validateApiKey(apiKey)) {
        throw new Error('Invalid API key format');
      }

      // 保存到安全存储
      await this.secretStorage.store('ai-agent.apiKey', apiKey);
      this.currentConfig.apiKey = apiKey;

      // 发布配置更新事件
      await this.eventBus.emit({
        type: 'config.api_key_updated',
        source: 'ConfigManager',
        hasApiKey: true,
      });

      console.log('API key updated successfully');
    } catch (error) {
      console.error('Failed to set API key:', error);
      throw error;
    }
  }

  /**
   * 删除API密钥
   */
  async clearApiKey(): Promise<void> {
    try {
      await this.secretStorage.delete('ai-agent.apiKey');
      this.currentConfig.apiKey = '';

      await this.eventBus.emit({
        type: 'config.api_key_cleared',
        source: 'ConfigManager',
        hasApiKey: false,
      });

      console.log('API key cleared successfully');
    } catch (error) {
      console.error('Failed to clear API key:', error);
      throw error;
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(updates: Partial<ConfigState>): Promise<void> {
    try {
      const oldConfig = { ...this.currentConfig };

      // 更新内存中的配置
      Object.assign(this.currentConfig, updates);

      // 保存到VS Code配置
      if (updates.baseUrl !== undefined) {
        await this.config.update('baseUrl', updates.baseUrl, vscode.ConfigurationTarget.Global);
      }
      if (updates.model !== undefined) {
        await this.config.update('model', updates.model, vscode.ConfigurationTarget.Global);
      }
      if (updates.temperature !== undefined) {
        await this.config.update('temperature', updates.temperature, vscode.ConfigurationTarget.Global);
      }
      if (updates.maxTokens !== undefined) {
        await this.config.update('maxTokens', updates.maxTokens, vscode.ConfigurationTarget.Global);
      }
      if (updates.provider !== undefined) {
        await this.config.update('provider', updates.provider, vscode.ConfigurationTarget.Global);
      }

      // 如果API密钥有更新，单独处理
      if (updates.apiKey !== undefined) {
        await this.setApiKey(updates.apiKey);
      }

      // 发布配置更新事件
      await this.eventBus.emit({
        type: 'config.updated',
        source: 'ConfigManager',
        oldConfig: this.getSafeConfigFromState(oldConfig),
        newConfig: this.getSafeConfig(),
        changes: Object.keys(updates),
      });

      console.log('Configuration updated successfully');
    } catch (error) {
      console.error('Failed to update configuration:', error);
      throw error;
    }
  }

  /**
   * 重置配置到默认值
   */
  async resetConfig(): Promise<void> {
    try {
      const defaultConfig: ConfigState = {
        apiKey: '',
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-4o',
        temperature: 0.2,
        maxTokens: 4096,
        provider: 'openai',
      };

      await this.updateConfig(defaultConfig);
      await this.clearApiKey();

      await this.eventBus.emit({
        type: 'config.reset',
        source: 'ConfigManager',
      });

      console.log('Configuration reset to defaults');
    } catch (error) {
      console.error('Failed to reset configuration:', error);
      throw error;
    }
  }

  /**
   * 验证配置
   */
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.currentConfig.apiKey) {
      errors.push('API key is required');
    }

    if (!this.currentConfig.baseUrl) {
      errors.push('Base URL is required');
    } else if (!this.isValidUrl(this.currentConfig.baseUrl)) {
      errors.push('Base URL is not a valid URL');
    }

    if (!this.currentConfig.model) {
      errors.push('Model is required');
    }

    if (this.currentConfig.temperature < 0 || this.currentConfig.temperature > 2) {
      errors.push('Temperature must be between 0 and 2');
    }

    if (this.currentConfig.maxTokens < 1 || this.currentConfig.maxTokens > 32000) {
      errors.push('Max tokens must be between 1 and 32000');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 从安全存储加载API密钥
   */
  private async loadApiKey(): Promise<void> {
    try {
      const apiKey = await this.secretStorage.get('ai-agent.apiKey');
      if (apiKey) {
        this.currentConfig.apiKey = apiKey;
      }
    } catch (error) {
      console.error('Failed to load API key from secure storage:', error);
    }
  }

  /**
   * 设置配置变化监听器
   */
  private setupConfigurationWatcher(): void {
    vscode.workspace.onDidChangeConfiguration((event) => {
      if (event.affectsConfiguration('ai-agent')) {
        this.handleConfigurationChange();
      }
    });
  }

  /**
   * 处理配置变化
   */
  private async handleConfigurationChange(): Promise<void> {
    try {
      const newConfig = vscode.workspace.getConfiguration('ai-agent');
      const oldConfig = { ...this.currentConfig };

      // 更新配置
      this.currentConfig.baseUrl = newConfig.get('baseUrl', this.currentConfig.baseUrl);
      this.currentConfig.model = newConfig.get('model', this.currentConfig.model);
      this.currentConfig.temperature = newConfig.get('temperature', this.currentConfig.temperature);
      this.currentConfig.maxTokens = newConfig.get('maxTokens', this.currentConfig.maxTokens);
      this.currentConfig.provider = newConfig.get('provider', this.currentConfig.provider) as LLMProvider;

      // 发布配置变化事件
      await this.eventBus.emit({
        type: 'config.external_change',
        source: 'ConfigManager',
        oldConfig: this.getSafeConfigFromState(oldConfig),
        newConfig: this.getSafeConfig(),
      });
    } catch (error) {
      console.error('Failed to handle configuration change:', error);
    }
  }

  /**
   * 验证API密钥格式
   */
  private validateApiKey(apiKey: string): boolean {
    if (!apiKey || typeof apiKey !== 'string') {
      return false;
    }

    // 基本长度检查
    if (apiKey.length < 10) {
      return false;
    }

    // OpenAI API密钥格式检查
    if (this.currentConfig.provider === 'openai') {
      return apiKey.startsWith('sk-') && apiKey.length >= 40;
    }

    // 其他提供者的基本检查
    return true;
  }

  /**
   * 验证URL格式
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 从配置状态获取安全配置
   */
  private getSafeConfigFromState(config: ConfigState): Omit<ConfigState, 'apiKey'> {
    const { apiKey, ...safeConfig } = config;
    return safeConfig;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    // 清理监听器等资源
  }
}
