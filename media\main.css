/* Main styles for AI Agent */
.container {
	padding: 16px;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

h1 {
	margin-bottom: 16px;
	font-size: 1.5em;
	font-weight: 600;
}

.chat-container {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
}

.header {
	display: flex;
	justify-content: flex-end;
	padding: 8px 0;
	border-bottom: 1px solid var(--vscode-panel-border);
	margin-bottom: 8px;
}

.clear-btn {
	background-color: var(--vscode-button-secondaryBackground);
	color: var(--vscode-button-secondaryForeground);
	border: 1px solid var(--vscode-button-border);
	padding: 4px 8px;
	border-radius: 3px;
	font-size: 12px;
	cursor: pointer;
}

.clear-btn:hover {
	background-color: var(--vscode-button-secondaryHoverBackground);
}

.header {
	display: flex;
	justify-content: flex-end;
	padding: 8px 0;
	border-bottom: 1px solid var(--vscode-panel-border);
	margin-bottom: 8px;
}

.clear-btn {
	background-color: var(--vscode-button-secondaryBackground);
	color: var(--vscode-button-secondaryForeground);
	border: 1px solid var(--vscode-button-border);
	padding: 4px 8px;
	border-radius: 2px;
	cursor: pointer;
	font-size: 12px;
}

.clear-btn:hover {
	background-color: var(--vscode-button-secondaryHoverBackground);
}

.messages {
	flex: 1;
	overflow-y: auto;
	padding: 8px 0;
	margin-bottom: 16px;
	border: 1px solid var(--vscode-panel-border);
	border-radius: 4px;
	background-color: var(--vscode-panel-background);
}

.message {
	margin: 8px 12px;
	padding: 8px 12px;
	border-radius: 8px;
	max-width: 85%;
}

.message.user {
	background-color: var(--vscode-button-background);
	color: var(--vscode-button-foreground);
	margin-left: auto;
	margin-right: 12px;
}

.message.assistant {
	background-color: var(--vscode-input-background);
	color: var(--vscode-input-foreground);
	border: 1px solid var(--vscode-input-border);
	margin-right: auto;
	margin-left: 12px;
}

.message.system {
	background-color: var(--vscode-badge-background);
	color: var(--vscode-badge-foreground);
	margin: 8px auto;
	text-align: center;
	font-style: italic;
	opacity: 0.8;
}

.message-content {
	word-wrap: break-word;
	line-height: 1.4;
}

.loading-indicator {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 8px 12px;
	background-color: var(--vscode-input-background);
	border: 1px solid var(--vscode-input-border);
	border-radius: 4px;
	margin: 8px 0;
	font-style: italic;
	opacity: 0.8;
}

.loading-spinner {
	width: 16px;
	height: 16px;
	border: 2px solid var(--vscode-progressBar-background);
	border-radius: 50%;
	border-top-color: var(--vscode-button-background);
	animation: spin 1s linear infinite;
}

.usage-info {
	text-align: right;
	font-size: 11px;
	color: var(--vscode-descriptionForeground);
	padding: 4px 0;
	margin-bottom: 8px;
}

.input-container {
	display: flex;
	gap: 8px;
	align-items: flex-end;
}

#messageInput {
	flex: 1;
	min-height: 32px;
	max-height: 120px;
	resize: none;
	font-family: var(--vscode-font-family);
	line-height: 1.4;
	padding: 8px;
}

#sendButton {
	min-width: 60px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* Scrollbar styling */
.messages::-webkit-scrollbar {
	width: 8px;
}

.messages::-webkit-scrollbar-track {
	background: var(--vscode-scrollbarSlider-background);
}

.messages::-webkit-scrollbar-thumb {
	background: var(--vscode-scrollbarSlider-background);
	border-radius: 4px;
}

.messages::-webkit-scrollbar-thumb:hover {
	background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Loading animation */
.loading {
	display: inline-block;
	width: 12px;
	height: 12px;
	border: 2px solid var(--vscode-progressBar-background);
	border-radius: 50%;
	border-top-color: var(--vscode-button-background);
	animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
	to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 400px) {
	.container {
		padding: 8px;
	}
	
	.message {
		max-width: 95%;
		margin: 6px 8px;
		padding: 6px 10px;
	}
	
	h1 {
		font-size: 1.3em;
		margin-bottom: 12px;
	}
}
