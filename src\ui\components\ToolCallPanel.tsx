/**
 * Tool Call Panel - 工具调用面板组件
 * 
 * 显示工具调用的实时执行状态和进度
 */

import React, { useState, useEffect } from 'react';
import { ToolCall } from '../../types';
import './ToolCallPanel.css';

export interface ToolCallPanelProps {
  toolCall: ToolCall;
  onComplete?: () => void;
  onUpdate?: (chunk: string) => void;
  onCancel?: () => void;
}

export const ToolCallPanel: React.FC<ToolCallPanelProps> = ({
  toolCall,
  onComplete,
  onUpdate,
  onCancel
}) => {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [logs, setLogs] = useState<string[]>([]);
  const [isCompleted, setIsCompleted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 工具执行步骤映射
  const toolSteps = {
    'refactor': [
      '分析代码结构',
      '识别重构机会',
      '生成重构方案',
      '验证代码正确性',
      '完成重构'
    ],
    'generateTests': [
      '分析代码逻辑',
      '识别测试场景',
      '生成测试用例',
      '验证测试覆盖率',
      '完成测试生成'
    ],
    'explainCode': [
      '解析代码语法',
      '分析代码逻辑',
      '识别设计模式',
      '生成解释文档',
      '完成代码解释'
    ],
    'executeTerminal': [
      '验证命令安全性',
      '准备执行环境',
      '执行命令',
      '收集输出结果',
      '完成命令执行'
    ],
    'ragQuery': [
      '解析查询意图',
      '搜索代码索引',
      '搜索知识库',
      '合并搜索结果',
      '完成智能搜索'
    ],
    'optimize': [
      '分析代码性能',
      '识别优化点',
      '生成优化建议',
      '验证优化效果',
      '完成性能分析'
    ]
  };

  const steps = toolSteps[toolCall.name as keyof typeof toolSteps] || [
    '初始化工具',
    '处理输入参数',
    '执行核心逻辑',
    '生成输出结果',
    '完成工具调用'
  ];

  // 模拟工具执行进度
  useEffect(() => {
    if (isCompleted || error) return;

    const totalSteps = steps.length;
    let currentStepIndex = 0;
    
    const progressInterval = setInterval(() => {
      if (currentStepIndex < totalSteps) {
        setCurrentStep(steps[currentStepIndex]);
        setProgress(((currentStepIndex + 1) / totalSteps) * 100);
        
        // 添加日志
        const timestamp = new Date().toLocaleTimeString();
        setLogs(prev => [...prev, `[${timestamp}] ${steps[currentStepIndex]}`]);
        
        // 模拟步骤更新
        onUpdate?.(`正在${steps[currentStepIndex]}...\n`);
        
        currentStepIndex++;
      } else {
        setIsCompleted(true);
        setProgress(100);
        setCurrentStep('执行完成');
        onComplete?.();
        clearInterval(progressInterval);
      }
    }, 1000 + Math.random() * 1000); // 随机延迟模拟真实执行

    return () => clearInterval(progressInterval);
  }, [steps, isCompleted, error, onComplete, onUpdate]);

  // 取消执行
  const handleCancel = () => {
    setError('用户取消执行');
    onCancel?.();
  };

  // 获取工具配置
  const getToolConfig = () => {
    const configs = {
      'refactor': { icon: '🔧', color: '#3b82f6', name: '代码重构' },
      'generateTests': { icon: '🧪', color: '#10b981', name: '生成测试' },
      'explainCode': { icon: '📖', color: '#8b5cf6', name: '代码解释' },
      'executeTerminal': { icon: '💻', color: '#f59e0b', name: '终端执行' },
      'ragQuery': { icon: '🔍', color: '#ef4444', name: 'RAG搜索' },
      'optimize': { icon: '⚡', color: '#06b6d4', name: '性能优化' }
    };

    return configs[toolCall.name as keyof typeof configs] || {
      icon: '🛠️',
      color: '#6b7280',
      name: toolCall.name
    };
  };

  const config = getToolConfig();

  return (
    <div className="tool-call-panel">
      {/* 面板头部 */}
      <div className="panel-header" style={{ borderTopColor: config.color }}>
        <div className="tool-info">
          <span className="tool-icon">{config.icon}</span>
          <div className="tool-details">
            <h3 className="tool-name">{config.name}</h3>
            <div className="tool-status">
              {error ? (
                <span className="status error">❌ 执行失败</span>
              ) : isCompleted ? (
                <span className="status completed">✅ 执行完成</span>
              ) : (
                <span className="status running">🔄 正在执行</span>
              )}
            </div>
          </div>
        </div>
        
        <div className="panel-actions">
          {!isCompleted && !error && (
            <button
              className="cancel-button"
              onClick={handleCancel}
              title="取消执行"
            >
              <i className="icon icon-x" />
            </button>
          )}
          <button
            className="minimize-button"
            title="最小化面板"
          >
            <i className="icon icon-minimize" />
          </button>
        </div>
      </div>

      {/* 进度条 */}
      <div className="progress-section">
        <div className="progress-info">
          <span className="current-step">{currentStep}</span>
          <span className="progress-percentage">{Math.round(progress)}%</span>
        </div>
        <div className="progress-bar">
          <div 
            className="progress-fill"
            style={{ 
              width: `${progress}%`,
              backgroundColor: error ? '#ef4444' : config.color
            }}
          />
        </div>
      </div>

      {/* 步骤列表 */}
      <div className="steps-section">
        <h4>执行步骤</h4>
        <div className="steps-list">
          {steps.map((step, index) => {
            const stepProgress = Math.max(0, Math.min(100, (progress / 100) * steps.length - index));
            const isActive = index === Math.floor((progress / 100) * steps.length);
            const isCompleted = stepProgress >= 1;
            
            return (
              <div 
                key={index}
                className={`step-item ${isActive ? 'active' : ''} ${isCompleted ? 'completed' : ''}`}
              >
                <div className="step-indicator">
                  {isCompleted ? (
                    <i className="icon icon-check" />
                  ) : isActive ? (
                    <div className="spinner-small" />
                  ) : (
                    <span className="step-number">{index + 1}</span>
                  )}
                </div>
                <span className="step-text">{step}</span>
                {isActive && (
                  <div className="step-progress">
                    <div 
                      className="step-progress-fill"
                      style={{ width: `${(stepProgress % 1) * 100}%` }}
                    />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* 执行日志 */}
      <div className="logs-section">
        <h4>执行日志</h4>
        <div className="logs-container">
          {logs.map((log, index) => (
            <div key={index} className="log-entry">
              {log}
            </div>
          ))}
          {error && (
            <div className="log-entry error">
              [ERROR] {error}
            </div>
          )}
        </div>
      </div>

      {/* 参数信息 */}
      {toolCall.parameters && Object.keys(toolCall.parameters).length > 0 && (
        <div className="parameters-section">
          <h4>执行参数</h4>
          <div className="parameters-list">
            {Object.entries(toolCall.parameters).map(([key, value]) => (
              <div key={key} className="parameter-item">
                <span className="parameter-key">{key}:</span>
                <span className="parameter-value">
                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 底部操作 */}
      <div className="panel-footer">
        {isCompleted && (
          <div className="completion-actions">
            <button className="action-button primary">
              <i className="icon icon-eye" />
              查看结果
            </button>
            <button className="action-button secondary">
              <i className="icon icon-download" />
              导出日志
            </button>
          </div>
        )}
        
        {error && (
          <div className="error-actions">
            <button className="action-button danger">
              <i className="icon icon-refresh" />
              重新执行
            </button>
            <button className="action-button secondary">
              <i className="icon icon-bug" />
              报告问题
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// 导出类型
export type { ToolCallPanelProps };
